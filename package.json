{"name": "ministudio-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch", "mcp:bts": "npx @agentdeskai/browser-tools-server@latest"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/client-integration-nextjs": "^0.12.2", "@clerk/nextjs": "^6.27.1", "@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@firebase/auth": "^1.11.0", "@heroui/react": "^2.8.2", "@hookform/resolvers": "^5.2.0", "@meltdownjs/cn": "^1.2.1", "@remotion/cli": "4.0.327", "@remotion/media-utils": "^4.0.327", "@remotion/player": "^4.0.327", "@remotion/webcodecs": "^4.0.327", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "cloudinary": "^2.7.0", "filepond": "^4.32.8", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-preview": "^4.6.12", "firebase": "^12.0.0", "framer-motion": "^12.23.11", "graphql-ws": "^6.0.6", "immer": "^10.1.1", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "15.4.4", "next-themes": "^0.4.6", "prettier": "^3.6.2", "react": "19.1.0", "react-dom": "19.1.0", "react-filepond": "^7.1.3", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-photo-view": "^1.2.7", "react-player": "^3.3.1", "react-resizable-panels": "^3.0.3", "remotion": "4.0.327", "uuid": "^11.1.0", "zod": "^4.0.13", "zod-from-json-schema": "^0.4.2", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/client-preset": "^4.8.3", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@next/eslint-plugin-next": "^15.4.5", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9", "eslint-config-next": "15.4.4", "eslint-plugin-react-hooks": "^5.2.0", "tailwindcss": "^4", "typescript": "^5", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}