#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Run linting with auto-fix
pnpm lint:fix

# Run formatting
pnpm format

# Check if there are any changes after linting/formatting
if [ -n "$(git diff --name-only)" ] || [ -n "$(git diff --cached --name-only)" ]; then
  echo "Changes detected after linting/formatting. Adding and committing..."
  git add .
  git commit -m "style: auto-fix linting and formatting issues"
fi
