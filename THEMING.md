# Hero UI Custom Theming Setup

This project includes a comprehensive Hero UI theming setup with three built-in themes:

## Available Themes

### 1. Light Theme (`light`)

- Clean, bright interface with blue primary colors
- Perfect for daytime use and professional applications

### 2. Dark Theme (`dark`)

- Dark background with bright accents
- Ideal for low-light environments and modern aesthetics

### 3. Mini Studio Theme (`ministudio`)

- Custom branded theme with purple/magenta gradient
- Dark base with vibrant purple primary colors
- Unique brand identity for the Mini Studio application

## Usage

### Theme Toggle Component

The `ThemeToggle` component allows users to cycle through all available themes:

```tsx
import { ThemeToggle } from "@/components/theme-toggle";

function MyComponent() {
  return <ThemeToggle />;
}
```

### Manual Theme Setting

You can programmatically set themes using the `useTheme` hook:

```tsx
import { useTheme } from "next-themes";

function MyComponent() {
  const { theme, setTheme } = useTheme();

  return (
    <div>
      <button onClick={() => setTheme("light")}>Light</button>
      <button onClick={() => setTheme("dark")}>Dark</button>
      <button onClick={() => setTheme("ministudio")}>Mini Studio</button>
    </div>
  );
}
```

### Hero UI Components with Theming

All Hero UI components automatically use the current theme:

```tsx
import { Button, Card, CardBody } from "@heroui/react";

function ThemedComponents() {
  return (
    <Card className="bg-content1">
      <CardBody>
        <Button color="primary">Primary Button</Button>
        <Button color="secondary">Secondary Button</Button>
        <Button color="success">Success Button</Button>
      </CardBody>
    </Card>
  );
}
```

## Theme Colors

Each theme includes semantic color tokens:

- `primary` - Main brand color
- `secondary` - Secondary accent color
- `success` - Success/positive actions
- `warning` - Warning/caution states
- `danger` - Error/destructive actions

## Customization

### Adding New Themes

To add a new theme, edit `tailwind.config.js`:

```js
themes: {
  // ... existing themes
  "my-custom-theme": {
    extend: "dark", // or "light"
    colors: {
      primary: {
        DEFAULT: "#your-color",
        foreground: "#ffffff",
      },
      // ... other colors
    },
  },
}
```

### Modifying Existing Themes

You can override specific colors in existing themes by modifying the theme object in `tailwind.config.js`.

## Layout Customization

The theme system also supports layout customization:

```js
layout: {
  spacingUnit: 4,
  disabledOpacity: 0.5,
  radius: {
    small: "8px",
    medium: "12px",
    large: "14px",
  },
  borderWidth: {
    small: "1px",
    medium: "2px",
    large: "3px",
  },
}
```

## Best Practices

1. **Use Semantic Colors**: Always use semantic color names (`primary`, `success`, etc.) instead of specific color values
2. **Test All Themes**: Ensure your components look good in all available themes
3. **Consistent Spacing**: Use the predefined spacing units for consistency
4. **Accessibility**: Ensure sufficient contrast ratios in all themes

## Development

- Run `pnpm dev` to start development server
- Use the theme toggle in the top-right corner to test different themes
- All theme changes are applied instantly without page refresh
