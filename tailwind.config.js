import { heroui } from "@heroui/react";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-inter)", "system-ui", "sans-serif"],
        mono: ["var(--font-mono)", "monospace"],
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      prefix: "heroui",
      addCommonColors: false,
      defaultTheme: "light",
      defaultExtendTheme: "light",
      layout: {
        spacingUnit: 4,
        disabledOpacity: 0.5,
        dividerWeight: "1px",
        fontSize: {
          tiny: "0.75rem",
          small: "0.875rem",
          medium: "1rem",
          large: "1.125rem",
        },
        lineHeight: {
          tiny: "1rem",
          small: "1.25rem",
          medium: "1.5rem",
          large: "1.75rem",
        },
        radius: {
          small: "8px",
          medium: "12px",
          large: "14px",
        },
        borderWidth: {
          small: "1px",
          medium: "2px",
          large: "3px",
        },
      },
      themes: {
        light: {
          layout: {},
          colors: {
            background: "#FFFFFF",
            foreground: "#11181C",
            card: {
              DEFAULT: "#FFFFFF",
              foreground: "#11181C",
            },
            popover: {
              DEFAULT: "#FFFFFF",
              foreground: "#11181C",
            },
            primary: {
              50: "#e6f1fe",
              100: "#cce3fd",
              200: "#99c7fb",
              300: "#66aaf9",
              400: "#338ef7",
              500: "#006FEE",
              600: "#005bc4",
              700: "#004493",
              800: "#002e62",
              900: "#001731",
              DEFAULT: "#006FEE",
              foreground: "#ffffff",
            },
            secondary: {
              50: "#f2f2f3",
              100: "#e6e6e7",
              200: "#ccccce",
              300: "#b3b3b6",
              400: "#99999d",
              500: "#808085",
              600: "#66666a",
              700: "#4d4d50",
              800: "#333335",
              900: "#1a1a1b",
              DEFAULT: "#808085",
              foreground: "#ffffff",
            },
            success: {
              50: "#e8fdf4",
              100: "#d1fae5",
              200: "#a7f3d0",
              300: "#6ee7b7",
              400: "#34d399",
              500: "#10b981",
              600: "#059669",
              700: "#047857",
              800: "#065f46",
              900: "#064e3b",
              DEFAULT: "#17c964",
              foreground: "#ffffff",
            },
            warning: {
              50: "#fefce8",
              100: "#fef9c3",
              200: "#fef08a",
              300: "#fde047",
              400: "#facc15",
              500: "#eab308",
              600: "#ca8a04",
              700: "#a16207",
              800: "#854d0e",
              900: "#713f12",
              DEFAULT: "#f5a524",
              foreground: "#ffffff",
            },
            danger: {
              50: "#fef2f2",
              100: "#fee2e2",
              200: "#fecaca",
              300: "#fca5a5",
              400: "#f87171",
              500: "#ef4444",
              600: "#dc2626",
              700: "#b91c1c",
              800: "#991b1b",
              900: "#7f1d1d",
              DEFAULT: "#f31260",
              foreground: "#ffffff",
            },
            focus: "#006FEE",
          },
        },
        dark: {
          layout: {},
          colors: {
            background: "#000000",
            foreground: "#ECEDEE",
            card: {
              DEFAULT: "#27272A",
              foreground: "#ECEDEE",
            },
            popover: {
              DEFAULT: "#27272A",
              foreground: "#ECEDEE",
            },
            primary: {
              50: "#001731",
              100: "#002e62",
              200: "#004493",
              300: "#005bc4",
              400: "#006FEE",
              500: "#338ef7",
              600: "#66aaf9",
              700: "#99c7fb",
              800: "#cce3fd",
              900: "#e6f1fe",
              DEFAULT: "#006FEE",
              foreground: "#ffffff",
            },
            secondary: {
              50: "#1a1a1b",
              100: "#333335",
              200: "#4d4d50",
              300: "#66666a",
              400: "#808085",
              500: "#99999d",
              600: "#b3b3b6",
              700: "#ccccce",
              800: "#e6e6e7",
              900: "#f2f2f3",
              DEFAULT: "#808085",
              foreground: "#000000",
            },
            success: {
              50: "#064e3b",
              100: "#065f46",
              200: "#047857",
              300: "#059669",
              400: "#10b981",
              500: "#34d399",
              600: "#6ee7b7",
              700: "#a7f3d0",
              800: "#d1fae5",
              900: "#e8fdf4",
              DEFAULT: "#17c964",
              foreground: "#000000",
            },
            warning: {
              50: "#713f12",
              100: "#854d0e",
              200: "#a16207",
              300: "#ca8a04",
              400: "#eab308",
              500: "#facc15",
              600: "#fde047",
              700: "#fef08a",
              800: "#fef9c3",
              900: "#fefce8",
              DEFAULT: "#f5a524",
              foreground: "#000000",
            },
            danger: {
              50: "#7f1d1d",
              100: "#991b1b",
              200: "#b91c1c",
              300: "#dc2626",
              400: "#ef4444",
              500: "#f87171",
              600: "#fca5a5",
              700: "#fecaca",
              800: "#fee2e2",
              900: "#fef2f2",
              DEFAULT: "#f31260",
              foreground: "#ffffff",
            },
            focus: "#006FEE",
          },
        },
        // Custom brand theme
        ministudio: {
          extend: "dark",
          colors: {
            background: "#0D001A",
            foreground: "#ffffff",
            primary: {
              50: "#3B096C",
              100: "#520A8A",
              200: "#7318A2",
              300: "#9823C2",
              400: "#c031e2",
              500: "#DD62ED",
              600: "#F182F6",
              700: "#FCADF9",
              800: "#FDD5F9",
              900: "#FEECFE",
              DEFAULT: "#DD62ED",
              foreground: "#ffffff",
            },
            focus: "#F182F6",
          },
          layout: {
            disabledOpacity: "0.3",
            radius: {
              small: "4px",
              medium: "6px",
              large: "8px",
            },
            borderWidth: {
              small: "1px",
              medium: "2px",
              large: "3px",
            },
          },
        },
      },
    }),
  ],
};

export default config;
