"use client";

import { ApolloLink, createHttpLink, split } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { onError } from "@apollo/client/link/error";
import { GraphQLWsLink } from "@apollo/client/link/subscriptions";
import { getMainDefinition } from "@apollo/client/utilities";
import { createClient } from "graphql-ws";
import {
  ApolloClient,
  ApolloNextAppProvider,
  InMemoryCache,
} from "@apollo/client-integration-nextjs";
import { useAuth } from "@clerk/nextjs";

// Custom provider that adds auth headers
export function ApolloWrapper({ children }: { children: React.ReactNode }) {
  const { getToken } = useAuth();

  // Create a custom makeClient that includes auth
  const makeClient = () => {
    const httpLink = createHttpLink({
      uri: process.env.NEXT_PUBLIC_GRAPHQL_API_URL,
    });

    const authLink = setContext(async (_, { headers }) => {
      const token = await getToken();
      const context = {
        headers: {
          ...headers,
          authorization: token ? `Bearer ${token}` : "",
        },
      };
      return context;
    });

    const errorLink = onError(({ graphQLErrors, networkError }) => {
      if (graphQLErrors) {
        graphQLErrors.forEach(({ message, locations, path }) => {
          console.error(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          );
        });
      }
      if (networkError) {
        console.error(`[Network error]: ${networkError}`);
      }
    });

    // Create WebSocket link for subscriptions
    const wsLink = new GraphQLWsLink(
      createClient({
        url: process.env.NEXT_PUBLIC_GRAPHQL_WS_URL!,
        connectionParams: async () => {
          const token = await getToken();
          return {
            authorization: token ? `Bearer ${token}` : "",
          };
        },
      })
    );

    // Split link - use WebSocket for subscriptions, HTTP for everything else
    const splitLink = split(
      ({ query }) => {
        const definition = getMainDefinition(query);
        return (
          definition.kind === "OperationDefinition" &&
          definition.operation === "subscription"
        );
      },
      wsLink,
      ApolloLink.from([errorLink, authLink, httpLink])
    );

    return new ApolloClient({
      cache: new InMemoryCache(),
      link: splitLink,
    });
  };

  return (
    <ApolloNextAppProvider makeClient={makeClient}>
      {children}
    </ApolloNextAppProvider>
  );
}
