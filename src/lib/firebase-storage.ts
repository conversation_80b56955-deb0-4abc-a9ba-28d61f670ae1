import {
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  UploadTaskSnapshot,
  UploadTask,
} from "firebase/storage";
import { storage } from "@/lib/firebase";
import { v4 as uuidv4 } from "uuid";

export interface UploadOptions {
  path?: string;
  fileName?: string;
  useUUID?: boolean;
  metadata?: Record<string, string>;
}

export interface UploadResult {
  url: string;
  path: string;
  fileName: string;
}

/**
 * Generates a storage path for the file
 */
export const generateStoragePath = (
  file: File,
  options: UploadOptions = {}
): { path: string; fileName: string } => {
  const {
    path = "uploads",
    fileName: customFileName,
    useUUID = true,
  } = options;

  let finalFileName: string;

  if (customFileName) {
    finalFileName = customFileName;
  } else if (useUUID) {
    const extension = file.name.split(".").pop();
    finalFileName = `${uuidv4()}.${extension}`;
  } else {
    finalFileName = file.name;
  }

  // Ensure path doesn't have leading or trailing slashes
  const normalizedPath = path.replace(/^\/|\/$/g, "");
  const fullPath = normalizedPath
    ? `${normalizedPath}/${finalFileName}`
    : finalFileName;

  return {
    path: fullPath,
    fileName: finalFileName,
  };
};

/**
 * Upload a file to Firebase Storage
 */
export const uploadFile = async (
  file: File,
  options: UploadOptions = {},
  onProgress?: (progress: number) => void
): Promise<UploadResult> => {
  const { path, fileName } = generateStoragePath(file, options);
  const storageRef = ref(storage, path);

  const uploadTask = uploadBytesResumable(storageRef, file, {
    contentType: file.type,
    customMetadata: options.metadata,
  });

  return new Promise((resolve, reject) => {
    uploadTask.on(
      "state_changed",
      (snapshot: UploadTaskSnapshot) => {
        const progress =
          (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        onProgress?.(progress);
      },
      error => {
        console.error("Upload error:", error);
        reject(error);
      },
      async () => {
        try {
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          resolve({
            url: downloadURL,
            path,
            fileName,
          });
        } catch (error) {
          reject(error);
        }
      }
    );
  });
};

/**
 * Delete a file from Firebase Storage
 */
export const deleteFile = async (path: string): Promise<void> => {
  const storageRef = ref(storage, path);
  await deleteObject(storageRef);
};

/**
 * Get an upload task for manual control (pause, resume, cancel)
 */
export const getUploadTask = (
  file: File,
  options: UploadOptions = {}
): { task: UploadTask; path: string; fileName: string } => {
  const { path, fileName } = generateStoragePath(file, options);
  const storageRef = ref(storage, path);
  const task = uploadBytesResumable(storageRef, file, {
    contentType: file.type,
    customMetadata: options.metadata,
  });

  return { task, path, fileName };
};
