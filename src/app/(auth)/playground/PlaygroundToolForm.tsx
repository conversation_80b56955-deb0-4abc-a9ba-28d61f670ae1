"use client";
import React from "react";
import { <PERSON>, CardHeader, CardBody } from "@heroui/react";
import { ToolForm } from "@/components/forms/ToolForm";
import { ToolInfo } from "@/graphql/generated/graphql";

interface PlaygroundToolFormProps {
  selectedTool: string;
  tools: ToolInfo[];
  onGenerate: (params: Record<string, unknown>) => void;
  isGenerating: boolean;
  generateLoading: boolean;
}

export const PlaygroundToolForm: React.FC<PlaygroundToolFormProps> = ({
  selectedTool,
  tools,
  onGenerate,
  isGenerating,
  generateLoading,
}) => {
  if (!selectedTool) return null;

  const tool = tools.find(t => t.key === selectedTool);

  return (
    <Card className="flex-1 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-[#f3ecf9] to-[#eaeafb] px-3 py-3">
        <h2 className="text-lg font-semibold text-[#3e3ed8]">
          {tool?.name || selectedTool}
        </h2>
      </CardHeader>
      <CardBody className="p-9">
        <ToolForm
          key={selectedTool}
          toolKey={selectedTool}
          onSubmit={onGenerate}
          isLoading={isGenerating || generateLoading}
        />
      </CardBody>
    </Card>
  );
};
