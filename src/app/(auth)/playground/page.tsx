"use client";
import React, { useState } from "react";
import useGenerationContext from "@/hooks/use-generation-context";
import {
  Generation,
  GenerationAsset,
  GenerationStatus,
  ToolInfo,
  useAvailableToolsQuery,
  useGenerateMutation,
  useGenerationByProjectSubscription,
  useGetGenerationQuery,
} from "@/graphql/generated/graphql";
import { Image, Select, SelectItem, Spinner } from "@heroui/react";
import { useUser } from "@/contexts/user-context";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import ReactPlayer from "react-player";
import { PhotoProvider, PhotoView } from "react-photo-view";
import "react-photo-view/dist/react-photo-view.css";
import { PlaygroundToolForm } from "./PlaygroundToolForm";

// Component: Tool Selector
interface ToolSelectorProps {
  tools: ToolInfo[];
  selectedTool: string;
  onToolChange: (tool: string) => void;
}

const ToolSelector: React.FC<ToolSelectorProps> = ({
  tools,
  selectedTool,
  onToolChange,
}) => {
  return (
    <div className="flex flex-col gap-2">
      <label className="text-xs font-semibold text-[#3e3ed8]">
        Pick a feature to test
      </label>
      <Select
        selectedKeys={selectedTool ? [selectedTool] : []}
        onSelectionChange={keys => {
          const selected = Array.from(keys)[0] as string;
          onToolChange(selected);
        }}
        placeholder="Select a tool"
        classNames={{
          trigger:
            "h-10 border-2 border-[#b2b2ef] bg-white hover:border-[#6565e0] data-[hover=true]:border-[#6565e0]",
          value: "text-sm text-[#11181c]",
          selectorIcon: "text-[#6565e0]",
        }}
      >
        {tools.map(tool => (
          <SelectItem key={tool.key}>{tool.name}</SelectItem>
        ))}
      </Select>
    </div>
  );
};

// Tool Form component removed - using PlaygroundToolForm instead

// Component: Input Panel
interface InputPanelProps {
  tools: ToolInfo[];
  selectedTool: string;
  onToolChange: (tool: string) => void;
  onGenerate: (params: Record<string, unknown>) => void;
  isGenerating: boolean;
  generateLoading: boolean;
}

const InputPanel: React.FC<InputPanelProps> = ({
  tools,
  selectedTool,
  onToolChange,
  onGenerate,
  isGenerating,
  generateLoading,
}) => {
  return (
    <Panel defaultSize={43.75} minSize={30} maxSize={60}>
      <div className="h-full bg-neutral-50 p-9">
        <div className="flex flex-col gap-9 h-full">
          <ToolSelector
            tools={tools}
            selectedTool={selectedTool}
            onToolChange={onToolChange}
          />
          <PlaygroundToolForm
            selectedTool={selectedTool}
            tools={tools}
            onGenerate={onGenerate}
            isGenerating={isGenerating}
            generateLoading={generateLoading}
          />
        </div>
      </div>
    </Panel>
  );
};

// Component: Generation Status
interface GenerationStatusProps {
  generation: Generation;
}

const GenerationStatusDisplay: React.FC<GenerationStatusProps> = ({
  generation,
}) => {
  return (
    <div className="mb-4">
      <h3 className="text-lg font-semibold text-[#3e3ed8] mb-2">
        Generation Result
      </h3>
      <div className="flex gap-2 text-sm">
        <span className="font-medium">Status:</span>
        <span
          className={`capitalize ${
            generation.status === GenerationStatus.Completed
              ? "text-green-600"
              : generation.status === GenerationStatus.Failed
                ? "text-red-600"
                : "text-yellow-600"
          }`}
        >
          {generation.status}
        </span>
      </div>
      {generation.progress && (
        <div className="flex gap-2 text-sm">
          <span className="font-medium">Progress:</span>
          <span>{generation.progress}%</span>
        </div>
      )}
    </div>
  );
};

// Component: Generation Results Panel
interface GenerationResultsPanelProps {
  generation?: Generation | null;
  isGenerating: boolean;
  generateLoading: boolean;
}

const GenerationResultsPanel: React.FC<GenerationResultsPanelProps> = ({
  generation,
  isGenerating,
  generateLoading,
}) => {
  return (
    <Panel defaultSize={56.25} minSize={40}>
      <div className="h-full bg-white p-9 overflow-auto">
        <div className="flex justify-center h-full">
          {!generation && !isGenerating && (
            <div className="text-center">
              <p className="text-gray-500">
                Select a tool and enter a prompt to generate content
              </p>
            </div>
          )}

          {(isGenerating || generateLoading) && (
            <div className="text-center">
              <Spinner size="lg" color="primary" />
              <p className="mt-4 text-gray-500">Generating...</p>
            </div>
          )}

          {generation && (
            <div className="w-full">
              <GenerationStatusDisplay generation={generation} />

              <PhotoProvider>
                <div className="grid grid-cols-1 gap-6">
                  {generation.result?.assets?.map((asset, index) => (
                    <GenerationAssetPreview
                      key={asset.url || index}
                      asset={asset}
                    />
                  ))}
                </div>
              </PhotoProvider>
            </div>
          )}
        </div>
      </div>
    </Panel>
  );
};

// Main Playground Page Component
const PlaygroundPage = () => {
  const { defaultProjectId } = useUser();
  const [selectedTool, setSelectedTool] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);

  const { data: toolsData } = useAvailableToolsQuery();
  const tools = toolsData?.availableTools || [];

  const { generateContext } = useGenerationContext({});
  const [generate, { data: generateData, loading: generateLoading }] =
    useGenerateMutation();

  const generationId = generateData?.generate.id;

  const { data: generationData } = useGetGenerationQuery({
    variables: {
      generationId,
    },
    skip: !generationId,
  });

  useGenerationByProjectSubscription({
    variables: {
      projectId: defaultProjectId || "",
    },
    skip: !defaultProjectId,
  });

  const handleGenerate = async (params: Record<string, unknown>) => {
    if (!selectedTool) return;

    setIsGenerating(true);
    try {
      await generate({
        variables: {
          tool: selectedTool,
          params,
          context: generateContext(),
        },
      });
    } catch (error) {
      console.error("Generation error:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="h-[calc(100vh-72px)] w-full bg-white">
      <PanelGroup direction="horizontal" className="h-full w-full">
        <InputPanel
          tools={tools}
          selectedTool={selectedTool}
          onToolChange={setSelectedTool}
          onGenerate={handleGenerate}
          isGenerating={isGenerating}
          generateLoading={generateLoading}
        />

        <PanelResizeHandle className="w-1 bg-neutral-200 hover:bg-neutral-300 transition-colors" />

        <GenerationResultsPanel
          generation={generationData?.generation}
          isGenerating={isGenerating}
          generateLoading={generateLoading}
        />
      </PanelGroup>
    </div>
  );
};

const GenerationAssetPreview = ({ asset }: { asset: GenerationAsset }) => {
  return (
    <div className="w-full">
      {asset.type === "image" && (
        <PhotoView src={asset.url}>
          <div className="cursor-pointer">
            <Image
              width={512}
              isBlurred
              alt={asset.url}
              src={asset.url}
              className="aspect-auto hover:opacity-90 transition-opacity"
            />
          </div>
        </PhotoView>
      )}
      {asset.type === "video" && (
        <div className="w-full">
          <ReactPlayer
            className="aspect-video"
            width="100%"
            height="100%"
            controls
            src={asset.url}
          />
        </div>
      )}
      {asset.type === "text" && <pre>{asset.url}</pre>}
    </div>
  );
};

export default PlaygroundPage;
