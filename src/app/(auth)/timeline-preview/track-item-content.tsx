import { AudioItem } from "@/components/timeline/audio-item";
import { ImageItem } from "@/components/timeline/image-item";
import { TrackTypeBadge } from "@/components/timeline/track-item";
import { Track, TrackItem } from "@/components/timeline/use-timeline-store";
import { VideoThumbnailItem } from "@/components/timeline/video-thumbnail-item";

export function TrackItemContent({
  item,
  track,
}: {
  item: TrackItem;
  track: Track;
}) {
  return (
    <div className="w-full h-full">
      {(track.mediaType === "video" ||
        (track.mediaType === "visual" && item.videoUrl)) && (
        <VideoThumbnailItem item={item} trackHeight={track.height ?? 40} />
      )}
      {(track.mediaType === "image" ||
        (track.mediaType === "visual" && item.imageUrl)) && (
        <ImageItem item={item} trackHeight={track.height ?? 40} />
      )}
      {track.mediaType === "audio" && (
        <AudioItem item={item} trackHeight={track.height ?? 40} />
      )}
      <TrackTypeBadge>{track.mediaType.toLocaleUpperCase()}</TrackTypeBadge>
    </div>
  );
}
