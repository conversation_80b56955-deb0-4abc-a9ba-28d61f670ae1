import { useEffect } from "react";
import { useTimelineContext } from "@/components/timeline/use-timeline-store";

export function LocalStoragePersister() {
  const tracks = useTimelineContext(s => s.tracks);
  const setTracks = useTimelineContext(s => s.setTracks);

  useEffect(() => {
    let initiated = false;
    for (const track of tracks) {
      if (track.items.length) {
        initiated = true;
        break;
      }
    }
    if (initiated) {
      localStorage.setItem("tracks", JSON.stringify(tracks));
    }
  }, [tracks]);

  useEffect(() => {
    const tracks = localStorage.getItem("tracks");
    if (tracks) {
      setTracks(JSON.parse(tracks));
    }
  }, [setTracks]);

  return null;
}
