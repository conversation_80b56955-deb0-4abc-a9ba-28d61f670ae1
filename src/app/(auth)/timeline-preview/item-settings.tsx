import { useTimelineContext } from "@/components/timeline/use-timeline-store";

export function ItemSettings() {
  const selected = useTimelineContext(s => s.selected);
  const setItem = useTimelineContext(s => s.setItem);
  const setSelectedItemId = useTimelineContext(s => s.setSelectedItemId);

  if (!selected) return null;

  return (
    <div className="p-4">
      <button onClick={() => setSelectedItemId(null)}>Close</button>

      <div>
        <label>Color</label>
        <input
          type="string"
          value={selected.item.color ?? ""}
          onChange={e => {
            setItem(selected.trackIndex, selected.itemIndex, {
              color: e.target.value,
            });
          }}
        />
      </div>

      <div>
        <label>Left</label>
        <input
          type="range"
          min="1"
          max="100"
          value={selected.item.left ?? 0}
          onChange={e =>
            setItem(selected.trackIndex, selected.itemIndex, {
              left: e.target.value,
            })
          }
        />
      </div>

      <div>
        <label>Top</label>
        <input
          type="range"
          min="1"
          max="100"
          value={selected.item.top ?? 0}
          onChange={e =>
            setItem(selected.trackIndex, selected.itemIndex, {
              top: e.target.value,
            })
          }
        />
      </div>

      {selected.track.mediaType === "text" && (
        <div>
          <label>Text</label>
          <input
            type="string"
            value={selected.item.text ?? ""}
            onChange={e => {
              setItem(selected.trackIndex, selected.itemIndex, {
                text: e.target.value,
              });
            }}
          />
        </div>
      )}
    </div>
  );
}
