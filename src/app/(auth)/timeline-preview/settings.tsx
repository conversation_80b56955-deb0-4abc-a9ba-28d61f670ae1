import { useState } from "react";
import {
  AspectRatio,
  getNextId,
  getTrackWidth,
} from "@/components/timeline/use-timeline-store";
import { useTimelineContext } from "@/components/timeline/use-timeline-store";

const narrations = [
  {
    name: "er - total narration",
    url: "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/total-narration-2.wav",
    duration: 98.7,
  },
];

const conversations = [
  {
    name: "test 1",
    url: "https://slickwid-public.s3.us-east-1.amazonaws.com/1.mp3",
    duration: 29,
  },
];

const sfxs = [
  {
    name: "sfx 1",
    url: "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music1.wav",
    duration: 32.8,
  },
  {
    name: "sfx 2",
    url: "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music2.wav",
    duration: 32.8,
  },
  {
    name: "sfx 3",
    url: "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music3.wav",
    duration: 32.8,
  },
  {
    name: "sfx 4",
    url: "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music4.wav",
    duration: 32.8,
  },
];

const visuals = [
  {
    name: "er - book 1 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-1.mp4",
    duration: 8,
  },
  {
    name: "er - book 2 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-2.mp4",
    duration: 8,
  },
  {
    name: "er - book 3 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-3.mp4",
    duration: 8,
  },
  {
    name: "er - book 4 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-4.mp4",
    duration: 8,
  },
  {
    name: "er - video 5 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/video-5.mp4",
    duration: 8,
  },
  {
    name: "er - video 6 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/video-6.mp4",
    duration: 8,
  },
  {
    name: "er - video 7 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/video-7.mp4",
    duration: 8,
  },
  {
    name: "er - video 8 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/video-8.mp4",
    duration: 8,
  },
  {
    name: "er - video 9 [v]",
    videoUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/video-9.mp4",
    duration: 8,
  },
  {
    name: "er - book 1 [i]",
    imageUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-img-1.jpg",
    duration: 1000,
    trimLeft: 494,
    trimRight: 494,
  },
  {
    name: "er - book 2 [i]",
    imageUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-img-2.jpg",
    duration: 1000,
    trimLeft: 494,
    trimRight: 494,
  },
  {
    name: "er - book 3 [i]",
    imageUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-img-3.jpg",
    duration: 1000,
    trimLeft: 494,
    trimRight: 494,
  },
  {
    name: "er - book 4 [i]",
    imageUrl:
      "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-img-4.jpg",
    duration: 1000,
    trimLeft: 494,
    trimRight: 494,
  },
];

type FileInputType = "audio" | "video" | "image";

function FileUpload({
  types,
  onChange,
}: {
  types: FileInputType[];
  onChange: (
    url: string,
    options: { duration?: number; fileType?: FileInputType }
  ) => void;
}) {
  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    const fileUrl = URL.createObjectURL(file);

    const isAudio = file.type.startsWith("audio/");
    const isVideo = file.type.startsWith("video/");
    const isImage = file.type.startsWith("image/");
    const fileType = isAudio
      ? "audio"
      : isVideo
        ? "video"
        : isImage
          ? "image"
          : undefined;

    if (isAudio) {
      const audio = new Audio(fileUrl);
      audio.addEventListener("loadedmetadata", () => {
        onChange(fileUrl, { duration: audio.duration, fileType });
      });
    } else if (isVideo) {
      const video = document.createElement("video");
      video.addEventListener("loadedmetadata", () => {
        onChange(fileUrl, { duration: video.duration, fileType });
      });
      video.src = fileUrl;
    } else if (isImage) {
      onChange(fileUrl, { fileType });
    }
    e.target.value = "";
  }

  const accept = types.map(type => `${type}/*`).join(",");

  return <input type="file" accept={accept} onChange={handleChange} />;
}

export function Settings() {
  const selected = useTimelineContext(s => s.selected);
  const addItem = useTimelineContext(s => s.addItem);
  const tracks = useTimelineContext(s => s.tracks);
  const aspectRatio = useTimelineContext(s => s.aspectRatio);
  const setAspectRatio = useTimelineContext(s => s.setAspectRatio);

  const [narrationIndex, setNarrationIndex] = useState<string>();
  const [conversationIndex, setConversationIndex] = useState<string>();
  const [visualIndex, setVisualIndex] = useState<string>();
  const [sfxIndex, setSfxIndex] = useState<string>();

  function handleAddNarration() {
    if (!narrationIndex) return;

    const narration = narrations[parseInt(narrationIndex)];

    addItem(0, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[0]),
      color: "#030898",

      trimLeft: 0,
      trimRight: 0,
      totalSize: narration.duration,
      audioUrl: narration.url,
    });
  }

  function handleAddConversation() {
    if (!conversationIndex) return;

    const conversation = conversations[parseInt(conversationIndex)];

    addItem(1, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[1]),
      color: "#0483C5",

      trimLeft: 0,
      trimRight: 0,
      totalSize: conversation.duration,
      audioUrl: conversation.url,
    });
  }

  function handleAddVisual() {
    if (!visualIndex) return;

    const visual = visuals[parseInt(visualIndex)];

    addItem(4, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[4]),
      color: "#1DC0BB",

      trimLeft: visual.trimLeft ?? 0,
      trimRight: visual.trimRight ?? 0,
      totalSize: visual.duration,
      videoUrl: visual.videoUrl,
      imageUrl: visual.imageUrl,

      width: 1920,
      height: 1080,
    });
  }

  function handleAddSfx() {
    if (!sfxIndex) return;

    const sfx = sfxs[parseInt(sfxIndex)];

    addItem(3, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[3]),
      color: "#0483C5",

      trimLeft: 0,
      trimRight: 0,
      totalSize: sfx.duration,
      audioUrl: sfx.url,
    });
  }

  function handleCopyJson() {
    const json = JSON.stringify(tracks, null, 2);
    navigator.clipboard.writeText(json);
    alert("JSON copied to clipboard");
  }

  function handleAddNarrationFromFile(
    url: string,
    options: { duration?: number }
  ) {
    addItem(0, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[0]),
      color: "#030898",
      trimLeft: 0,
      trimRight: 0,
      totalSize: options.duration ?? 0,
      audioUrl: url,
    });
  }

  function handleAddVisualFromFile(
    url: string,
    options: { duration?: number; fileType?: FileInputType }
  ) {
    addItem(4, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[4]),
      color: "#1DC0BB",

      trimLeft: 0,
      trimRight: 0,
      totalSize: options.duration ?? 10,
      imageUrl: options.fileType === "image" ? url : undefined,
      videoUrl: options.fileType === "video" ? url : undefined,
    });
  }

  function handleAddSfxFromFile(url: string, options: { duration?: number }) {
    addItem(3, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[3]),
      color: "#0483C5",

      trimLeft: 0,
      trimRight: 0,
      totalSize: options.duration ?? 0,
      audioUrl: url,
    });
  }

  function handleAddConversationFromFile(
    url: string,
    options: { duration?: number }
  ) {
    addItem(1, {
      id: getNextId().toString(),
      start: getTrackWidth(tracks[1]),
      color: "#0483C5",

      trimLeft: 0,
      trimRight: 0,
      totalSize: options.duration ?? 0,
      audioUrl: url,
    });
  }

  if (selected) return null;

  return (
    <div className="p-4 flex flex-col gap-4">
      <div className="flex items-center gap-2 justify-between">
        <span>Aspect Ratio</span>
        <select
          value={aspectRatio}
          onChange={e => setAspectRatio(e.target.value as AspectRatio)}
        >
          <option value="16:9">16:9</option>
          <option value="9:16">9:16</option>
          <option value="1:1">1:1</option>
        </select>
      </div>

      <div>
        <div>Narration</div>
        <select
          value={narrationIndex}
          onChange={e => setNarrationIndex(e.target.value)}
        >
          <option value="">Select a narration</option>
          {narrations.map((narration, index) => (
            <option key={index} value={index}>
              {narration.name}
            </option>
          ))}
        </select>
        <button onClick={handleAddNarration}>Add</button>
        <FileUpload types={["audio"]} onChange={handleAddNarrationFromFile} />
      </div>

      <div>
        <div>Conversation</div>
        <select
          value={conversationIndex}
          onChange={e => setConversationIndex(e.target.value)}
        >
          <option value="">Select a conversation</option>
          {conversations.map((conversation, index) => (
            <option key={index} value={index}>
              {conversation.name}
            </option>
          ))}
        </select>
        <button onClick={handleAddConversation}>Add</button>
        <FileUpload
          types={["audio"]}
          onChange={handleAddConversationFromFile}
        />
      </div>

      <div>
        <div>Visual</div>
        <select
          value={visualIndex}
          onChange={e => setVisualIndex(e.target.value)}
        >
          <option value="">Select a visual</option>
          {visuals.map((visual, index) => (
            <option key={index} value={index}>
              {visual.name}
            </option>
          ))}
        </select>
        <button onClick={handleAddVisual}>Add</button>
        <FileUpload
          types={["video", "image"]}
          onChange={handleAddVisualFromFile}
        />
      </div>

      <div>
        <div>SFX</div>
        <select value={sfxIndex} onChange={e => setSfxIndex(e.target.value)}>
          <option value="">Select a sfx</option>
          {sfxs.map((sfx, index) => (
            <option key={index} value={index}>
              {sfx.name}
            </option>
          ))}
        </select>
        <button onClick={handleAddSfx}>Add</button>
        <FileUpload types={["audio"]} onChange={handleAddSfxFromFile} />
      </div>

      <div>
        <button onClick={handleCopyJson}>Copy JSON</button>
      </div>
    </div>
  );
}
