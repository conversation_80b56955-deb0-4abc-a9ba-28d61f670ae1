"use client";

import { useRef } from "react";
import { TimelinePlayer } from "@/components/remotion/timeline-player";
import { Controls } from "@/components/timeline/controls";
import { Timeline } from "@/components/timeline/timeline";
import { Track } from "@/components/timeline/use-timeline-store";
import { TimelineProvider } from "@/components/timeline/use-timeline-provider";
import { ItemSettings } from "./item-settings";
import { Settings } from "./settings";
import { TrackItemContent } from "./track-item-content";

const initTracks: Track[] = [
  {
    id: "1",
    items: [],
    type: "default",
    mediaType: "audio",
    title: "Narration",
    purpose: "narration",
    height: 50,
  },
  {
    id: "2",
    items: [],
    type: "default",
    mediaType: "audio",
    title: "Conversation",
    purpose: "conversation",
    height: 50,
  },
  {
    id: "3",
    items: [],
    type: "default",
    mediaType: "audio",
    title: "Conversation",
    purpose: "conversation",
    height: 50,
  },
  {
    id: "4",
    items: [],
    type: "default",
    mediaType: "audio",
    title: "SFX",
    purpose: "sfx",
    height: 50,
  },
  {
    id: "5",
    items: [],
    type: "ordered",
    mediaType: "visual",
    title: "Visual",
    purpose: "visual",
    height: 50,
  },
];

export default function TimelinePage() {
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div className="border-t border-gray-200 flex flex-col h-[100vh]">
      <TimelineProvider tracks={initTracks} containerRef={containerRef}>
        <div className="flex flex-1">
          <div className="w-[340px] min-w-[340px] border-r border-gray-200">
            <ItemSettings />
            <Settings />
          </div>
          <TimelinePlayer containerRef={containerRef} />
        </div>
        <div>
          <Controls />
          <Timeline
            containerRef={containerRef}
            renderer={({ trackItem, track }) => {
              return <TrackItemContent item={trackItem} track={track} />;
            }}
          />
        </div>
      </TimelineProvider>
    </div>
  );
}
