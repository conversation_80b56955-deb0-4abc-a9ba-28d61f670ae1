"use client";

import React from "react";
import DynamicFormBuilder, {
  FieldConfig,
} from "@/components/DynamicFormBuilder/DynamicFormBuilder";

export default function FileUploadFormExample() {
  const fields: FieldConfig[] = [
    {
      name: "name",
      type: "text",
      label: "Name",
      placeholder: "Enter your name",
      required: true,
    },
    {
      name: "profileImage",
      type: "file",
      label: "Profile Image",
      accept: "image/*",
      storagePath: "profiles/images",
      useUUID: true,
      description: "Upload your profile picture",
      required: true,
    },
    {
      name: "resume",
      type: "file",
      label: "Resume",
      accept: "application/pdf,.pdf",
      storagePath: "profiles/resumes",
      useUUID: true,
      description: "Upload your resume (PDF only)",
    },
    {
      name: "description",
      type: "textarea",
      label: "Description",
      placeholder: "Tell us about yourself",
      rows: 4,
    },
  ];

  const handleSubmit = (data: Record<string, unknown>) => {
    console.log("Form submitted with data:", data);

    // Example of accessing file URLs:
    if (
      data.profileImage &&
      typeof data.profileImage === "object" &&
      "url" in data.profileImage
    ) {
      const profileImage = data.profileImage as {
        url: string;
        path: string;
        fileName: string;
      };
      console.log("Profile image URL:", profileImage.url);
      console.log("Profile image path:", profileImage.path);
      console.log("Profile image filename:", profileImage.fileName);
    }

    if (
      data.resume &&
      typeof data.resume === "object" &&
      "url" in data.resume
    ) {
      const resume = data.resume as {
        url: string;
        path: string;
        fileName: string;
      };
      console.log("Resume URL:", resume.url);
      console.log("Resume path:", resume.path);
      console.log("Resume filename:", resume.fileName);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">File Upload Form Example</h1>

      <DynamicFormBuilder
        fields={fields}
        onSubmit={handleSubmit}
        layout="single"
        submitButton={{
          label: "Submit Form",
          color: "primary",
        }}
      />

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="font-bold mb-2">How it works:</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>File fields automatically upload to Firebase Storage</li>
          <li>
            On successful upload, the form stores an object with url, path, and
            fileName
          </li>
          <li>You can access these values in the onSubmit handler</li>
          <li>Files are uploaded to the specified storagePath</li>
          <li>If useUUID is true, files are renamed with unique IDs</li>
        </ul>
      </div>
    </div>
  );
}
