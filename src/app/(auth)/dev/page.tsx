"use client";

import { useAuth } from "@clerk/nextjs";
import { useState } from "react";
import { useUser } from "@/contexts/user-context";
import FileUploadFormExample from "./file-upload-form-example";

export default function DevPage() {
  const { getToken } = useAuth();
  const {
    recentProjects,
    defaultProjectId,
    isLoading,
    error,
    syncUser,
    setDefaultProjectId,
  } = useUser();
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleGetToken = async () => {
    setLoading(true);
    try {
      const token = await getToken({ template: "long-lived" });
      console.log("Token:", token);
      setToken(token);
    } catch (error) {
      console.error("Error getting token:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h1 className="text-2xl font-bold mb-6">Dev Utilities</h1>

      <div className="space-y-6">
        {/* Clerk Token Utility */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Clerk Token</h2>

          <button
            onClick={handleGetToken}
            disabled={loading}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? "Getting Token..." : "Get Token"}
          </button>

          {token && (
            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">
                Your Clerk JWT token:
              </p>
              <div className="bg-gray-100 p-3 rounded overflow-x-auto">
                <code className="text-xs break-all">{token}</code>
              </div>

              <button
                onClick={() => navigator.clipboard.writeText(token)}
                className="mt-2 text-sm text-blue-500 hover:text-blue-700"
              >
                Copy to clipboard
              </button>
            </div>
          )}
        </div>

        {/* User Context Values */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">User Context</h2>

          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Loading:</span>{" "}
              {isLoading ? "Yes" : "No"}
            </div>
            <div>
              <span className="font-medium">Error:</span>{" "}
              {error ? error.message : "None"}
            </div>
            <div>
              <span className="font-medium">Default Project ID:</span>{" "}
              {defaultProjectId || "Not set"}
            </div>
            <div>
              <span className="font-medium">Recent Projects:</span>
              {recentProjects.length > 0 ? (
                <ul className="mt-2 space-y-1">
                  {recentProjects.map(project => (
                    <li key={project.id} className="flex items-center gap-2">
                      <span>
                        - {project.name} ({project.id})
                      </span>
                      {project.id !== defaultProjectId && (
                        <button
                          onClick={() => setDefaultProjectId(project.id)}
                          className="text-xs text-blue-500 hover:text-blue-700"
                        >
                          Set as default
                        </button>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <span className="text-gray-500"> No projects</span>
              )}
            </div>
          </div>

          <button
            onClick={syncUser}
            disabled={isLoading}
            className="mt-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
          >
            {isLoading ? "Syncing..." : "Sync User"}
          </button>
        </div>

        {/* File Upload Form Example */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">
            File Upload Form Example
          </h2>
          <FileUploadFormExample />
        </div>

        {/* Space for more utilities */}
      </div>
    </div>
  );
}
