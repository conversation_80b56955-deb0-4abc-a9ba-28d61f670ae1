"use client";

//import { currentUser } from '@clerk/nextjs/server';
import { StoryboardHeader } from "@/components/storyboard/header";
import { Tool, ToolSidebar } from "@/components/storyboard/tool-sidebar";
import { Empty } from "@/components/storyboard/empty";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useCallback, useState } from "react";
import ShotIcon from "../../../assets/shot.svg";
import AnimateIcon from "../../../assets/animate.svg";
import NarrateIcon from "../../../assets/narrate.svg";
import AudioIcon from "../../../assets/audio.svg";
import CharacterIcon from "../../../assets/character.svg";
import { SvgIcon } from "@/components/common/svg-icon";
import { ToolSidebarShots } from "@/components/storyboard/tool-sidebar-shots";
import { ToolSidebarNarrator } from "@/components/storyboard/tool-sidebar-narrator";
import { ToolSidebarAudio } from "@/components/storyboard/tool-sidebar-audio";
import { Timeline } from "@/components/storyboard/timeline";
import { ToolSidebarAnimate } from "@/components/storyboard/tool-sidebar-animate";
import { ToolSidebarCharacters } from "@/components/storyboard/tool-sidebar-characters";
import { useSidebarStore } from "@/stores";
import {
  MediaType,
  Track,
  TrackItem,
  TrackType,
} from "@/components/timeline/use-timeline-store";

export default function StoryboardPage() {
  /*const user = await currentUser();

  if (!user) {
    return null;
  }*/
  const activeTab = useSidebarStore(state => state.activeTab);
  const setActiveTab = useSidebarStore(state => state.setActiveTab);

  const [tracks, setTracks] = useState<Track[]>([]);

  const addTrack = useCallback(
    (
      type: TrackType,
      mediaType: MediaType,
      title: string,
      purpose: string,
      items: TrackItem[]
    ) => {
      setTracks([
        ...tracks,
        {
          id: Math.random().toString(),
          type,
          mediaType,
          title,
          purpose,
          items,
        },
      ]);
    },
    [tracks]
  );

  const tools: Tool[] = [
    {
      value: "characters",
      label: "Characters",
      icon: <SvgIcon src={CharacterIcon} alt="Characters" />,
    },
    {
      value: "shots",
      label: "Frames",
      icon: <SvgIcon src={ShotIcon} alt="Frames" />,
    },
    {
      value: "animate",
      label: "Animate",
      icon: <SvgIcon src={AnimateIcon} alt="Animate" />,
    },
    {
      value: "narrator",
      label: "Voice Over",
      icon: <SvgIcon src={NarrateIcon} alt="Voice Over" />,
    },
    {
      value: "audio",
      label: "Audio",
      icon: <SvgIcon src={AudioIcon} alt="Audio" />,
    },
  ];

  return (
    <div className="h-[calc(100vh-64px)] w-full">
      <PanelGroup direction="vertical" className="h-full">
        <Panel defaultSize={60} className="h-full">
          <StoryboardHeader title="Matteo and the Lost Treasure" />
          <div className="flex h-full px-4 pb-4 gap-4">
            {/* Sidebar */}
            <ToolSidebar
              tools={tools}
              activeTool={activeTab}
              setActiveTool={setActiveTab}
            />

            {/* Tool Panels */}
            <div className="flex flex-col gap-4">
              <ToolSidebarCharacters
                active={activeTab === "characters"}
                onClose={() => setActiveTab(null)}
              />
              <ToolSidebarShots
                active={activeTab === "shots"}
                onClose={() => setActiveTab(null)}
                addTrack={addTrack}
              />
              <ToolSidebarAnimate
                active={activeTab === "animate"}
                onClose={() => setActiveTab(null)}
              />
              <ToolSidebarNarrator
                active={activeTab === "narrator"}
                onClose={() => setActiveTab(null)}
                addTrack={addTrack}
              />
              <ToolSidebarAudio
                active={activeTab === "audio"}
                onClose={() => setActiveTab(null)}
                addTrack={addTrack}
              />
            </div>

            {/* Empty / Preview */}
            <div className="flex-1 flex justify-center items-center overflow-auto scrollbar-hide">
              <Empty />
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="py-[7.5px] flex justify-center cursor-row-resize">
          <div className="h-2 w-20 bg-indigo-100 rounded-full" />
        </PanelResizeHandle>

        <Panel minSize={10} maxSize={45}>
          <Timeline tracks={tracks} />
        </Panel>
      </PanelGroup>
    </div>
  );
}
