"use client";

import { useCharactersQuery } from "@/graphql/generated/graphql";
import Link from "next/link";
import Image from "next/image";
import Button from "@/components/Button/Button";
import { SvgIcon } from "@/components/common/svg-icon";
import DefaultAvatar from "@/assets/default-avatar.svg";

export default function CharactersPage() {
  const { data, loading, error } = useCharactersQuery();

  if (loading)
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );

  if (error)
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <p className="text-red-600">
          Error loading characters: {error.message}
        </p>
      </div>
    );

  const characters = data?.characters || [];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Characters</h1>
        <Link href="/characters/add">
          <Button color="primary" variant="solid">
            Create Character
          </Button>
        </Link>
      </div>

      {characters.length === 0 ? (
        <div className="text-center py-16">
          <div className="mx-auto w-32 h-32 mb-4">
            <SvgIcon
              src={DefaultAvatar}
              alt="No characters"
              className="w-full h-full opacity-20"
            />
          </div>
          <h2 className="text-xl font-semibold mb-2">No characters yet</h2>
          <p className="text-gray-600 mb-6">
            Create your first character to get started
          </p>
          <Link href="/characters/add">
            <Button color="primary" variant="solid">
              Create First Character
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {characters.map(character => (
            <div
              key={character._id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
            >
              <div className="aspect-square relative bg-gray-100">
                {character.image?.url ? (
                  <Image
                    src={character.image.url}
                    alt={character.name}
                    className="w-full h-full object-cover"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <SvgIcon
                      src={DefaultAvatar}
                      alt={character.name}
                      className="w-24 h-24 opacity-40"
                    />
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-lg">{character.name}</h3>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
