"use client";

import {
  useCreateCharacterMutation,
  CharactersDocument,
} from "@/graphql/generated/graphql";
import { useRouter } from "next/navigation";
import {
  DynamicFormBuilder,
  FieldConfig,
} from "@/components/DynamicFormBuilder/DynamicFormBuilder";
import { FieldValues } from "react-hook-form";
import { useState } from "react";
import useGenerationContext from "@/hooks/use-generation-context";

export default function AddCharacterPage() {
  const router = useRouter();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const { generateContext } = useGenerationContext({});

  const [createCharacterMutation, { loading }] = useCreateCharacterMutation({
    refetchQueries: [{ query: CharactersDocument }],
    onCompleted: () => {
      router.push("/characters");
    },
    onError: error => {
      console.error("Error creating character:", error);
      alert(`Error creating character: ${error.message}`);
    },
  });

  const fields: FieldConfig[] = [
    {
      name: "name",
      type: "text",
      label: "Character Name",
      placeholder: "Enter character name",
      required: true,
      gridSpan: "full",
    },
    {
      name: "image",
      type: "file",
      label: "Character Image",
      accept: "image/*",
      gridSpan: "full",
    },
  ];

  const handleSubmit = async (data: FieldValues) => {
    try {
      // Handle image upload if provided
      let finalImageUrl = imageUrl;
      if (data.image?.url) {
        finalImageUrl = data.image.url;
      }

      // Generate context for the character creation
      const context = generateContext();

      await createCharacterMutation({
        variables: {
          input: {
            name: data.name,
            imageUrl: finalImageUrl,
            context: context,
          },
        },
      });
    } catch (error) {
      console.error("Error in form submission:", error);
      // If it's a context error, show a more specific message
      if (
        error instanceof Error &&
        error.message.includes("Default project ID")
      ) {
        alert(
          "Please ensure you have a default project selected before creating a character."
        );
      }
    }
  };

  const onValuesChange = (values: Record<string, unknown>) => {
    // Handle image URL update when file is uploaded
    if (
      values.image &&
      typeof values.image === "object" &&
      "url" in values.image
    ) {
      setImageUrl((values.image as { url: string }).url);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Create New Character</h1>
        <p className="text-gray-600">Add a character to your project</p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <DynamicFormBuilder
          fields={fields}
          onSubmit={handleSubmit}
          onValuesChange={onValuesChange}
          layout="single"
          submitButton={{
            label: loading ? "Creating..." : "Create Character",
            color: "primary",
            disabled: loading,
          }}
        />
      </div>

      <div className="mt-6 text-center">
        <button
          onClick={() => router.push("/characters")}
          className="text-gray-600 hover:text-gray-800 underline"
        >
          Cancel and go back
        </button>
      </div>
    </div>
  );
}
