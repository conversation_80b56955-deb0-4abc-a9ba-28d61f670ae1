import React from "react";
import { Slider, SliderCard } from "@/components/common/Slider";

const SliderDemoPage: React.FC = () => {
  const slides = [
    <SliderCard key="slide1">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2 opacity-90">mini</h2>
        <h2 className="text-3xl font-bold opacity-90">studio</h2>
      </div>
    </SliderCard>,

    <SliderCard key="slide2">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2 opacity-90">mini</h2>
        <h2 className="text-3xl font-bold opacity-90">studio</h2>
      </div>
    </SliderCard>,

    <SliderCard key="slide3">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2 opacity-90">mini</h2>
        <h2 className="text-3xl font-bold opacity-90">studio</h2>
      </div>
    </SliderCard>,

    <SliderCard key="slide4">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2 opacity-90">mini</h2>
        <h2 className="text-3xl font-bold opacity-90">studio</h2>
      </div>
    </SliderCard>,
  ];

  // Example with custom content
  const customSlides = [
    <SliderCard
      key="custom1"
      className="bg-gradient-to-br from-blue-300 via-blue-400 to-blue-500"
    >
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-4">Slide 1</h3>
        <p className="text-lg opacity-90">Custom content here</p>
      </div>
    </SliderCard>,

    <SliderCard
      key="custom2"
      className="bg-gradient-to-br from-green-300 via-green-400 to-green-500"
    >
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-4">Slide 2</h3>
        <p className="text-lg opacity-90">Another slide content</p>
      </div>
    </SliderCard>,

    <SliderCard
      key="custom3"
      className="bg-gradient-to-br from-pink-300 via-pink-400 to-pink-500"
    >
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-4">Slide 3</h3>
        <p className="text-lg opacity-90">More custom content</p>
      </div>
    </SliderCard>,
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-12">
      <div className="container mx-auto px-4">
        <h1 className="text-4xl font-bold text-center mb-12 text-gray-800 dark:text-white">
          Slider Demo
        </h1>

        {/* Original Design Slider */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold mb-6 text-gray-700 dark:text-gray-300">
            Original Design (Auto-play enabled)
          </h2>
          <Slider
            items={slides}
            autoPlay={true}
            autoPlayInterval={3000}
            className="group"
          />
        </div>

        {/* Custom Colors Slider */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold mb-6 text-gray-700 dark:text-gray-300">
            Custom Colors (Manual navigation)
          </h2>
          <Slider items={customSlides} autoPlay={false} className="group" />
        </div>

        {/* Usage Instructions */}
        <div className="max-w-4xl mx-auto mt-16 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
            How to Use
          </h2>
          <div className="space-y-4 text-gray-600 dark:text-gray-300">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                Basic Import:
              </h3>
              <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md overflow-x-auto">
                <code>{`import { Slider, SliderCard } from '@/components/common/Slider';`}</code>
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                Basic Usage:
              </h3>
              <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md overflow-x-auto text-sm">
                <code>{`const slides = [
  <SliderCard key="1">
    <h2>Your Content</h2>
  </SliderCard>,
  // ... more slides
];

<Slider
  items={slides}
  autoPlay={true}
  autoPlayInterval={3000}
/>`}</code>
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                Features:
              </h3>
              <ul className="list-disc list-inside space-y-2">
                <li>Shows 1.5 slides (partial view of next slide)</li>
                <li>Smooth animations with Framer Motion</li>
                <li>Bottom dot navigation exactly like your design</li>
                <li>Auto-play support with customizable interval</li>
                <li>Touch/swipe support on mobile</li>
                <li>Customizable card backgrounds and content</li>
                <li>Accessible navigation with ARIA labels</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SliderDemoPage;
