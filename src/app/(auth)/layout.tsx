import React from "react";
import Logo from "@/assets/logo.svg";
import { SvgIcon } from "@/components/common/svg-icon";
import AuthenticatedProviders from "@/providers/authenticated.providers";
import Link from "next/link";
import { UserButton } from "@clerk/nextjs";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthenticatedProviders>
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-gradient-to-r from-purple-50 to-indigo-50">
          <div className="px-5">
            <div className="flex justify-between h-16">
              <div className="flex items-center gap-8">
                <Link href="/dashboard">
                  <SvgIcon
                    src={Logo}
                    alt="Logo"
                    width={65}
                    height={35}
                    className="cursor-pointer"
                  />
                </Link>
                <div className="flex items-center gap-6">
                  <Link
                    href="/storyboard"
                    className="text-gray-700 hover:text-gray-900 font-medium"
                  >
                    Storyboard
                  </Link>
                  <Link
                    href="/playground"
                    className="text-gray-700 hover:text-gray-900 font-medium"
                  >
                    Playground
                  </Link>
                </div>
              </div>
              <div className="flex gap-2 items-center">
                <UserButton />
                {/*<div className="flex flex-col gap-4 text-[#3E3ED8]">*/}
                {/*  <h2 className="text-[14px] leading-0 font-bold">User Name</h2>*/}
                {/*  <Link*/}
                {/*    href=""*/}
                {/*    className="text-[12px] underline leading-0 font-semibold"*/}
                {/*  >*/}
                {/*    logout*/}
                {/*  </Link>*/}
                {/*</div>*/}
              </div>
            </div>
          </div>
        </nav>
        <main>{children}</main>
      </div>
    </AuthenticatedProviders>
  );
}
