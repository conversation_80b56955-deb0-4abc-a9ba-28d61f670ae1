import "./global.css";
import { GlobalProviders } from "../providers/global.providers";
import { ReactNode } from "react";

export const metadata = {
  title: "Welcome to Mini Studio",
  description: "Generated by create-nx-workspace",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="min-h-screen bg-background text-foreground font-sans antialiased">
        <GlobalProviders>{children}</GlobalProviders>
      </body>
    </html>
  );
}
