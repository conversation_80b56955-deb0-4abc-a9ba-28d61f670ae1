import { SignedIn, SignedOut, SignIn<PERSON><PERSON>on, SignUpButton } from "@clerk/nextjs";
import Link from "next/link";

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-white to-gray-100">
      <div className="max-w-4xl mx-auto text-center px-4">
        <h1 className="text-5xl font-bold mb-6">Welcome to MiniStudio</h1>

        <SignedOut>
          <div className="space-x-4">
            <SignInButton mode="modal">
              <button className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition">
                Sign In
              </button>
            </SignInButton>
            <SignUpButton mode="modal">
              <button className="px-6 py-3 bg-gray-800 text-white font-medium rounded-lg hover:bg-gray-900 transition">
                Sign Up
              </button>
            </SignUpButton>
          </div>
        </SignedOut>

        <SignedIn>
          <Link href="/dashboard">
            <button className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition">
              Go to Dashboard
            </button>
          </Link>
        </SignedIn>
      </div>
    </div>
  );
}
