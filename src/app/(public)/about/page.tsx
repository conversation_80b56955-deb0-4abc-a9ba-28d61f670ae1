"use client";

import { useAuthRedirect } from "@/hooks/use-auth-redirect";
import Link from "next/link";

export default function AboutPage() {
  const { isSignedIn, isLoaded } = useAuthRedirect();

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-4xl font-bold mb-6">About MiniStudio</h1>
        <p className="text-gray-600 mb-4">
          This is a public page that anyone can access.
        </p>

        {isSignedIn ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-green-800">
              You are currently signed in! You can access protected pages.
            </p>
            <Link
              href="/dashboard"
              className="text-green-600 hover:text-green-800 underline"
            >
              Go to Dashboard →
            </Link>
          </div>
        ) : (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <p className="text-blue-800">
              You are not signed in. Sign in to access protected features.
            </p>
            <Link
              href="/sign-in"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Sign In →
            </Link>
          </div>
        )}

        <div className="mt-8">
          <Link
            href="/"
            className="text-gray-600 hover:text-gray-800 underline"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
