import { create } from "zustand";

type ToolParams = {
  text?: boolean;
  image?: boolean;
  audio?: boolean;
  video?: boolean;
};

type ToolMapper = {
  getTool: (params: ToolParams) => string;
};

export const useToolMapper = create<ToolMapper>(() => ({
  getTool: ({ text, image, audio, video }) => {
    if (text && image) return "human-avatar-from-text-and-image";
    if (text && audio) return "voice-to-video";
    if (text && video) return "subtitles-generator";
    if (image && video) return "character-generator";
    if (text) return "human-avatar-from-text";
    if (image) return "shot-generator";
    if (audio) return "music-from-audio";
    if (video) return "topaz-upscale-video";
    return "";
  },
}));
