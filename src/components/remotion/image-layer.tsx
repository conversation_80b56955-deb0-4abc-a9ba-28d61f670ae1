import { AbsoluteFill, Img } from "remotion";
import { TrackItem } from "../timeline/use-timeline-store";

export function ImageLayer({ item }: { item: TrackItem }) {
  if (!item.imageUrl) {
    return null;
  }

  return (
    <AbsoluteFill>
      <Img
        src={item.imageUrl}
        style={{ objectFit: "cover", width: "100%", height: "100%" }}
        pauseWhenLoading
      />
    </AbsoluteFill>
  );
}
