import { AbsoluteFill, OffthreadVideo, useVideoConfig } from "remotion";
import { TrackItem } from "../timeline/use-timeline-store";
import { secToFrame } from "./util";

export function VideoLayer({ item }: { item: TrackItem }) {
  const { fps } = useVideoConfig();

  if (!item.videoUrl) {
    return null;
  }

  return (
    <AbsoluteFill>
      <OffthreadVideo
        src={item.videoUrl}
        startFrom={secToFrame(item.trimLeft, fps)}
        pauseWhenBuffering
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
        }}
      />
    </AbsoluteFill>
  );
}
