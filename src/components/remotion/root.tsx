import { Composition } from "remotion";
import { TimelineVideo } from "./timeline-video";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="TimelineVideo"
        component={TimelineVideo}
        calculateMetadata={async ({ props }) => {
          const fps = 30;

          return {
            props,
            fps,
            width: 1920,
            height: 1080,
            durationInFrames: 23 * fps,
          };
        }}
        defaultProps={{
          tracks: [
            {
              id: "1",
              items: [
                {
                  id: "0",
                  start: 0,
                  color: "#030898",
                  trimLeft: 0,
                  trimRight: 0,
                  totalSize: 98.7,
                  audioUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/total-narration.wav",
                },
              ],
              type: "default",
              mediaType: "audio",
              title: "Narration",
              purpose: "narration",
              height: 40,
            },
            {
              id: "2",
              items: [],
              type: "default",
              mediaType: "audio",
              title: "Conversation",
              purpose: "conversation",
              height: 40,
            },
            {
              id: "3",
              items: [
                {
                  id: "2",
                  start: 0,
                  color: "#0483C5",
                  trimLeft: 0,
                  trimRight: 24.43,
                  totalSize: 32.8,
                  audioUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music1.wav",
                },
                {
                  id: "5",
                  start: 8.369999999999997,
                  color: "#0483C5",
                  trimLeft: 0,
                  trimRight: 16.65,
                  totalSize: 32.8,
                  audioUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music2.wav",
                },
                {
                  id: "11",
                  start: 24.519999999999996,
                  color: "#0483C5",
                  trimLeft: 0,
                  trimRight: 0,
                  totalSize: 32.8,
                  audioUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/music3.wav",
                },
              ],
              type: "default",
              mediaType: "audio",
              title: "SFX",
              purpose: "sfx",
              height: 30,
            },
            {
              id: "4",
              items: [
                {
                  id: "1",
                  start: 0,
                  color: "#1DC0BB",
                  trimLeft: 0,
                  trimRight: 0,
                  totalSize: 8,
                  videoUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-1.mp4",
                },
                {
                  id: "6",
                  start: 23.969999999999995,
                  color: "#1DC0BB",
                  trimLeft: 494,
                  trimRight: 503.57,
                  totalSize: 1000,
                  imageUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-img-1.jpg",
                },
                {
                  id: "3",
                  start: 8.029999999999998,
                  color: "#1DC0BB",
                  trimLeft: 0,
                  trimRight: 0,
                  totalSize: 8,
                  videoUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-2.mp4",
                },
                {
                  id: "7",
                  start: 26.469999999999995,
                  color: "#1DC0BB",
                  trimLeft: 496.5,
                  trimRight: 501.67,
                  totalSize: 1000,
                  imageUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-img-1.jpg",
                },
                {
                  id: "4",
                  start: 8.029999999999998,
                  color: "#1DC0BB",
                  trimLeft: 0,
                  trimRight: 0,
                  totalSize: 8,
                  videoUrl:
                    "https://slickwid-public.s3.us-east-1.amazonaws.com/mini-studio-assets/elara-ran/book-3.mp4",
                },
              ],
              type: "ordered",
              mediaType: "visual",
              title: "Visual",
              purpose: "visual",
              height: 50,
            },
          ],
        }}
      />
    </>
  );
};
