import { AbsoluteFill, Audio, useVideoConfig } from "remotion";
import { TrackItem } from "../timeline/use-timeline-store";
import { secToFrame } from "./util";

export function AudioLayer({ item }: { item: TrackItem }) {
  const { fps } = useVideoConfig();

  if (!item.audioUrl) {
    return null;
  }

  return (
    <AbsoluteFill>
      <Audio
        src={item.audioUrl}
        startFrom={secToFrame(item.trimLeft, fps)}
        pauseWhenBuffering
      />
    </AbsoluteFill>
  );
}
