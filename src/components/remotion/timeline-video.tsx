import {
  AbsoluteFill,
  random,
  Sequence,
  Series,
  useVideoConfig,
} from "remotion";
import { Track, TrackItem } from "../timeline/use-timeline-store";
import { secToFrame } from "./util";
import { useMemo } from "react";
import { VideoLayer } from "./video-layer";
import { AudioLayer } from "./audio-layer";
import { ImageLayer } from "./image-layer";

function ItemLayer({ item, track }: { item: TrackItem; track: Track }) {
  const left = useMemo(() => item.left ?? random(item.id) * 100, [item]);
  const top = useMemo(() => item.top ?? random(item.id + 1) * 100, [item]);

  if (
    track.mediaType === "video" ||
    (track.mediaType === "visual" && item.videoUrl)
  ) {
    return <VideoLayer item={item} />;
  }

  if (track.mediaType === "audio" && item.audioUrl) {
    return <AudioLayer item={item} />;
  }

  if (
    track.mediaType === "image" ||
    (track.mediaType === "visual" && item.imageUrl)
  ) {
    return <ImageLayer item={item} />;
  }

  return (
    <AbsoluteFill
      style={{
        backgroundColor: item.color,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        fontSize: 100,
        width: "900px",
        height: "fit-content",
        left: `${left}%`,
        top: `${top}%`,
      }}
    >
      {item.text ?? item.id}
    </AbsoluteFill>
  );
}

function OrderedTrackLayer({ track }: { track: Track }) {
  const { fps } = useVideoConfig();
  return (
    <Series>
      {track.items.map(item => (
        <Series.Sequence
          key={item.id}
          durationInFrames={secToFrame(
            item.totalSize - item.trimLeft - item.trimRight,
            fps
          )}
        >
          <ItemLayer item={item} track={track} />
        </Series.Sequence>
      ))}
    </Series>
  );
}

function DefaultTrackLayer({ track }: { track: Track }) {
  const { fps } = useVideoConfig();

  return (
    <>
      {track.items.map(item => (
        <Sequence
          key={item.id}
          from={secToFrame(item.start, 30)}
          durationInFrames={secToFrame(
            item.totalSize - item.trimLeft - item.trimRight,
            fps
          )}
        >
          <ItemLayer item={item} track={track} />
        </Sequence>
      ))}
    </>
  );
}

function TrackLayer({ track }: { track: Track }) {
  if (track.type === "ordered") {
    return <OrderedTrackLayer track={track} />;
  }
  return <DefaultTrackLayer track={track} />;
}

export function TimelineVideo({ tracks }: { tracks: Track[] }) {
  return (
    <AbsoluteFill style={{ backgroundColor: "white" }}>
      {tracks.map(track => (
        <TrackLayer key={track.id} track={track} />
      ))}
    </AbsoluteFill>
  );
}
