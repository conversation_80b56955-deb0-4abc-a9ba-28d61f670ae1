import cn from "@meltdownjs/cn";
import { Player } from "@remotion/player";
import { TimelineVideo } from "./timeline-video";
import { useEffect, useRef, useState } from "react";
import { useTimelinePlayer } from "./use-timeline-player";
import { secToFrame } from "./util";
import { useTimelineContext } from "../timeline/use-timeline-store";

export function TimelinePlayer({
  containerRef,
}: {
  containerRef: React.RefObject<HTMLDivElement | null>;
}) {
  const tracks = useTimelineContext(s => s.tracks);
  const size = useTimelineContext(s => s.size);
  const { playerRef, fps, compositionDimensions } =
    useTimelinePlayer(containerRef);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
  }>({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    if (playerContainerRef.current) {
      const rect = playerContainerRef.current.getBoundingClientRect();
      let height = rect.height;
      let width =
        (height * compositionDimensions.width) / compositionDimensions.height;
      if (width > rect.width) {
        width = rect.width;
        height =
          (rect.width * compositionDimensions.height) /
          compositionDimensions.width;
      }
      setDimensions({ width, height });
    }
  }, [compositionDimensions]);

  return (
    <div
      ref={playerContainerRef}
      className={cn(
        "w-full h-full flex justify-center items-center bg-gray-100"
      )}
    >
      <Player
        ref={playerRef}
        component={TimelineVideo}
        durationInFrames={secToFrame(Math.max(size, 1), fps)}
        compositionWidth={compositionDimensions.width}
        compositionHeight={compositionDimensions.height}
        style={{
          width: dimensions.width,
          height: dimensions.height,
        }}
        fps={fps}
        controls
        inputProps={{
          tracks,
        }}
        className="border border-gray-200"
        acknowledgeRemotionLicense
      />
    </div>
  );
}
