import { useEffect, useMemo, useRef, useState } from "react";
import { CallbackListener, PlayerRef } from "@remotion/player";
import { useTimelineContext } from "../timeline/use-timeline-store";

export function useTimelinePlayer(
  containerRef: React.RefObject<HTMLDivElement | null>
) {
  const playerRef = useRef<PlayerRef>(null);
  const updateHeadAtExternal = useTimelineContext(s => s.updateHeadAtExternal);
  const centerHead = useTimelineContext(s => s.centerHead);
  const focusHead = useTimelineContext(s => s.focusHead);
  const aspectRatio = useTimelineContext(s => s.aspectRatio);
  const [fps, setFps] = useState(30);
  const compositionDimensions = useMemo(() => {
    if (aspectRatio === "9:16") {
      return {
        width: 1080,
        height: 1920,
      };
    }
    if (aspectRatio === "1:1") {
      return {
        width: 1080,
        height: 1080,
      };
    }
    return {
      width: 1920,
      height: 1080,
    };
  }, [aspectRatio]);

  useEffect(() => {
    if (!playerRef.current || !containerRef.current) return;

    const player = playerRef.current;
    const timeline = containerRef.current;
    const onFrameUpdate: CallbackListener<"frameupdate"> = e => {
      updateHeadAtExternal(e.detail.frame / fps);
    };

    const onHeadAt = (e: CustomEvent<{ at: number }>) => {
      player.seekTo(Math.floor(e.detail.at * fps));
    };

    player.addEventListener("frameupdate", onFrameUpdate);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    timeline.addEventListener("headupdated", onHeadAt as any);
    return () => {
      player.removeEventListener("frameupdate", onFrameUpdate);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      timeline.removeEventListener("headupdated", onHeadAt as any);
    };
  }, [fps, updateHeadAtExternal, containerRef, centerHead, focusHead]);

  return {
    playerRef,
    fps,
    setFps,
    compositionDimensions,
  };
}
