"use client";

import { Button } from "@heroui/react";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const toggleTheme = () => {
    if (theme === "light") {
      setTheme("dark");
    } else if (theme === "dark") {
      setTheme("ministudio");
    } else {
      setTheme("light");
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case "light":
        return "🌞 Light";
      case "dark":
        return "🌙 Dark";
      case "ministudio":
        return "✨ Mini Studio";
      default:
        return "🎨 Theme";
    }
  };

  return (
    <Button variant="bordered" onPress={toggleTheme} className="min-w-unit-24">
      {getThemeLabel()}
    </Button>
  );
}
