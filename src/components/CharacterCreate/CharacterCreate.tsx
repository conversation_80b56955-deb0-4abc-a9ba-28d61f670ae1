import Button from "../Button/Button";
import ButtonGroup from "../ButtonGroup/ButtonGroup";
import { useState } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { Form, Input, Select, SelectItem } from "@heroui/react";
import UploadPage from "../UploadPage/UploadPage";

export interface CharacterCreateProps {
  open: boolean;
  onClose: () => void;
}

interface CharacterFormInput {
  name: string;
  type: string;
  description?: string;
}

export default function CharacterCreate({
  open,
  onClose,
}: CharacterCreateProps) {
  const { register, handleSubmit, watch } = useForm<CharacterFormInput>({
    defaultValues: {
      name: "",
      type: "human",
      description: "",
    },
  });
  const [selectCreateCharacterBtn, setSelectCreateCharacterBtn] =
    useState(false);
  const watchType = watch("type");

  if (!open) {
    return null;
  }

  const onSubmit: SubmitHandler<CharacterFormInput> = data => console.log(data);

  return (
    <div className="fixed inset-50 bg-white shadow-lg z-50 flex flex-col w-fit">
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-4">Characters</h2>
        <div>
          <ButtonGroup>
            <Button
              color="primary"
              onClick={() => setSelectCreateCharacterBtn(true)}
              variant="solid"
            >
              Create Character
            </Button>
            <Button
              color="secondary"
              onClick={() => setSelectCreateCharacterBtn(false)}
              variant="solid"
            >
              Library
            </Button>
          </ButtonGroup>
        </div>

        {selectCreateCharacterBtn && (
          <Form
            className="w-full max-w-xs flex flex-col gap-4 my-4"
            onSubmit={e => {
              e.preventDefault();
              handleSubmit(onSubmit)();
            }}
          >
            <Input
              isRequired
              errorMessage="Please enter a valid character name"
              label="Character Name"
              labelPlacement="outside"
              placeholder="Enter your character name"
              type="text"
              {...register("name", { required: true, maxLength: 30 })}
            />

            <Select
              className="mt-2"
              defaultSelectedKeys={["human"]}
              isRequired
              errorMessage="Please select a character type"
              label="Character Type"
              labelPlacement="outside"
              placeholder="Choose character type"
              {...register("type", { required: true })}
            >
              <SelectItem key="human">Human character</SelectItem>
              <SelectItem key="non-human">Non-human character</SelectItem>
            </Select>
            {watchType === "non-human" && (
              <Input
                className="my-2"
                isRequired
                errorMessage="Please enter a valid character description"
                label="Describe your character"
                labelPlacement="outside"
                placeholder="Enter your character description"
                type="text"
                {...register("description", { required: true })}
              />
            )}
            {watchType === "human" && <UploadPage />}
            <Button
              color="secondary"
              className="mt-4"
              onClick={() => console.log("Send req to server")} // send request to server for create character
              type="submit"
              variant="solid"
            >
              Create a New Character
            </Button>
          </Form>
        )}
        {!selectCreateCharacterBtn && (
          <div className="text-gray-600">
            This is the library of characters.
          </div>
        )}
        <Button
          color="primary"
          variant="solid"
          onClick={onClose}
          className="mt-4 display-inline-flex"
        >
          Close
        </Button>
      </div>
    </div>
  );
}
