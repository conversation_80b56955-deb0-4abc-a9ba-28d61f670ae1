import React, { FC, useCallback, useEffect, useState } from "react";
import Modal from "../common/Modal/Modal";
import DynamicFormBuilder, {
  FieldConfig,
} from "../DynamicFormBuilder/DynamicFormBuilder";
import { InfoBubble } from "@/components/common/info-bubble";
import PurbRightPNG from "@/assets/purp-right.png";
import Image from "next/image";
import Logo from "@/assets/logo.svg";
import { Slider, SliderCard } from "@/components/common/Slider";
import styles from "./styles.module.css";
import { useAvailableToolsQuery } from "@/graphql/generated/graphql";

interface CreateCharacterModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateCharacterModal: FC<CreateCharacterModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [generating, setGenerating] = useState<boolean>(true);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [helpTitle, setHelpTitle] = useState<string>("No character yet!");
  const [helpDescription, setHelpDescription] = useState<string>(
    "Once generated, your character will appear right here."
  );

  const { data: toolsData } = useAvailableToolsQuery();
  const tools = toolsData?.availableTools || [];

  console.log("toolsData", tools.map((tool) => tool.name));

  const slides = [
    <SliderCard key="slide1" className="h-full">
      <Image src={Logo} alt="Card image" width={90} />
    </SliderCard>,
    <SliderCard key="slide2" className="h-full">
      <Image src={Logo} alt="Card image" width={90} />
    </SliderCard>,
    <SliderCard key="slide3" className="h-full">
      <Image src={Logo} alt="Card image" width={90} />
    </SliderCard>,
    <SliderCard key="slide3" className="h-full">
      <Image src={Logo} alt="Card image" width={90} />
    </SliderCard>,
  ];

  // const [generateCharacterMutation] = useGenerateCharacterMutation({
  //   variables: {
  //     tool: "avatar-gen",
  //     params: {
  //       prompt: "boy",
  //       input_mode: 3,
  //       input_style: 1,
  //     },
  //   },
  // });

  useEffect(() => {
    if (!generating) {
      return;
    }

    const id = setTimeout(() => {
      setGeneratedImages([
        "https://png.pngtree.com/png-vector/20240715/ourmid/pngtree-happy-cartoon-character-boy-png-image_13113225.png",
        "https://i.pinimg.com/474x/2e/f0/8d/2ef08d67a5a71ec6a1beaf3a39a9fe16.jpg",
      ]);
      setGenerating(false);
    }, 3000);

    return () => clearTimeout(id);
  }, [generating]);

  const fields: FieldConfig[] = [
    {
      name: "name",
      type: "text",
      label: "Character name",
      placeholder: "Give your character a name...",
      required: true,
      gridSpan: "full",
    },
    {
      name: "type",
      type: "select",
      label: "Choose character type",
      placeholder: "Select here",
      required: true,
      options: tools.map(tool => ({
        value: tool.key,
        label: tool.name
      })),
      gridSpan: "full",
    },
    {
      name: "description",
      type: "textarea",
      label: "Describe your character",
      placeholder: "Describe clothing, expression, style, or context...",
      required: false,
      renderCondition: values => Boolean(values.type),
      gridSpan: "full",
    },
    {
      name: "image",
      type: "file",
      label: "Upload your Photo",
      accept: "image/png, image/jpeg",
      required: false,
      renderCondition: values => values.type === "human",
      gridSpan: "full",
    },
  ];

  const handleSubmit = useCallback(async () => {
    setGenerating(true);
    // TODO: Use the data to generate character
    // const name = data.name as string;
    // const type = data.type as string;
    // const description = data.description as string;
    // const image = data.image as File | null;
    //await generateCharacterMutation();
  }, []);

  const onValuesChanged = useCallback((values: Record<string, unknown>) => {
    if (values.image) {
      setHelpTitle("Nice choice!");
      setHelpDescription("That image will work perfectly.");
    } else if (values.description) {
      setHelpTitle("Amazing!");
      setHelpDescription("Loving the details you’re adding!");
    } else if (values.type) {
      setHelpTitle("Who are we creating today?");
      setHelpDescription("Select a character type to get started!");
    }
  }, []);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Create character"
      width={1100}
    >
      <div className="flex w-full">
        <div className="w-[342px] p-4 bg-white rounded-l-lg overflow-auto h-[590px]">
          <DynamicFormBuilder
            fields={fields}
            onSubmit={handleSubmit}
            defaultValues={{ name: "", type: "", description: "", image: null }}
            layout="single"
            submitButton={{
              label: "Create a new character",
              color: "primary",
              variant: "solid",
              className: "w-full my-4",
            }}
            className="h-full"
            formClassName="flex flex-col"
            onValuesChange={onValuesChanged}
          />
        </div>
        <div className="relative overflow-hidden w-[758px] h-[740px] shadow-[0px_1px_2px_0px_#0000000D] bg-gradient-to-r from-[#FFE0FD] to-[#D8D8F7]">
          {!generating && !generatedImages.length ? (
            <>
              <div className="absolute right-[64px] bottom-[207px] w-[299px]">
                <InfoBubble title={helpTitle} details={helpDescription} />
              </div>
              <Image
                src={PurbRightPNG.src}
                alt="purp..."
                className="absolute bottom-0 right-0"
                width={340}
                height={350}
              />
            </>
          ) : (
            <div className="p-7 h-full flex flex-col gap-6">
              <div className="text-indigo-500 text-base">
                Swipe through the options and choose the one you like best.
              </div>
              <div className={`${styles["scrollbar-left"]} flex gap-5 h-full overflow-auto`}>
                <Slider
                  items={slides}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default CreateCharacterModal;
