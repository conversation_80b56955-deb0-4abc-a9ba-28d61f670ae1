import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import AccordionPanel from "./AccordionPanel";

describe("AccordionPanel", () => {
  const items = [
    {
      key: "1",
      title: "Accordion 1",
      subtitle: "Click to expand",
      content: "Content 1 here",
    },
    {
      key: "2",
      title: "Accordion 2",
      content: "Content 2 here",
    },
    {
      key: "3",
      title: "Accordion 3",
      subtitle: (
        <span>
          Expandable <strong>item 3</strong>
        </span>
      ),
      content: "Content 3 here",
    },
  ];

  it("renders all accordion titles", () => {
    render(<AccordionPanel items={items} />);
    expect(screen.getByText("Accordion 1")).toBeInTheDocument();
    expect(screen.getByText("Accordion 2")).toBeInTheDocument();
    expect(screen.getByText("Accordion 3")).toBeInTheDocument();
  });

  it("renders subtitles correctly", () => {
    render(<AccordionPanel items={items} />);
    expect(screen.getByText("Click to expand")).toBeInTheDocument();
    expect(screen.getByText("Expandable")).toBeInTheDocument();
    expect(screen.getByText("item 3")).toBeInTheDocument();
  });

  it("renders content correctly", () => {
    render(<AccordionPanel items={items} />);
    expect(screen.getByText("Content 1 here")).toBeInTheDocument();
    expect(screen.getByText("Content 2 here")).toBeInTheDocument();
    expect(screen.getByText("Content 3 here")).toBeInTheDocument();
  });
});
