import { Accordion, AccordionItem } from "@heroui/react";
import { ReactNode, FC } from "react";

export interface AccordionItemData {
  key: string;
  title: string | ReactNode;
  subtitle?: string | ReactNode;
  content: string | ReactNode;
}

interface AccordionPanelProps {
  items: AccordionItemData[];
  className?: string;
}

const AccordionPanel: FC<AccordionPanelProps> = ({ items, className }) => {
  return (
    <Accordion className={className}>
      {items.map(({ key, title, subtitle, content }) => (
        <AccordionItem
          key={key}
          aria-label={typeof title === "string" ? title : `Accordion-${key}`}
          title={title}
          subtitle={subtitle}
        >
          {content}
        </AccordionItem>
      ))}
    </Accordion>
  );
};

export default AccordionPanel;
