import { render, screen } from "@testing-library/react";
import AvatarGroup from "./AvatarGroup";

const mockUrls = [
  "https://i.pravatar.cc/150?u=1",
  "https://i.pravatar.cc/150?u=2",
  "https://i.pravatar.cc/150?u=3",
];

describe("AvatarGroup", () => {
  it("renders all avatars from the avatarUrls prop", () => {
    render(<AvatarGroup avatarUrls={mockUrls} />);

    mockUrls.forEach(url => {
      const avatarImg = screen.getByRole("img", { name: "" });
      expect(avatarImg).toHaveAttribute("src", url);
    });
  });

  it("renders the AvatarGroup container", () => {
    render(<AvatarGroup avatarUrls={mockUrls} />);
    const group = screen.getByTestId("avatar-group");
    expect(group).toBeInTheDocument();
  });
});
