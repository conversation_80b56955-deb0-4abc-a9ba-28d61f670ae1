import { AvatarGroup } from "@heroui/react";
import Avatar from "../Avatar/Avatar";

export interface AvatarGroupComponentProps {
  avatarUrls: string[];
  isBordered?: boolean;
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
}

export default function AvatarGroupComponent({
  avatarUrls,
  isBordered = true,
  color = "default",
}: AvatarGroupComponentProps) {
  return (
    <AvatarGroup isBordered={isBordered} data-testid="avatar-group">
      {avatarUrls.map((url, index) => (
        <Avatar key={index} src={url} isBordered={isBordered} color={color} />
      ))}
    </AvatarGroup>
  );
}
