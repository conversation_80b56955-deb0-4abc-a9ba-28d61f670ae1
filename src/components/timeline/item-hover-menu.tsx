import { useEffect, useState } from "react";
import { HoverState, useTimelineContext } from "./use-timeline-store";
import { TbScissors, TbTrash } from "react-icons/tb";
import cn from "@meltdownjs/cn";

type Position = "top" | "bottom";

function Arrow({ position }: { position: Position }) {
  return (
    <div
      className={cn(
        "absolute top-0 left-1/2 -translate-x-1/2 w-4 h-2",
        position === "bottom" && "-translate-y-[100%]"
      )}
      style={{
        zIndex: 11,
        transform: position === "top" ? "scaleY(-1)" : undefined,
        top: position === "bottom" ? "0" : "100%",
      }}
    >
      {/* Gray border arrow */}
      <div
        className={cn(
          "absolute left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-gray-300"
        )}
        style={{ top: 0 }}
      />
      {/* White fill arrow */}
      <div
        className={cn(
          "absolute left-1/2 -translate-x-1/2 w-0 h-0 border-l-7 border-r-7 border-b-7 border-l-transparent border-r-transparent border-b-white"
        )}
        style={{ top: 1 }}
      />
    </div>
  );
}

function MenuItem({
  children,
  onClick,
}: {
  children: React.ReactNode;
  onClick?: () => void;
}) {
  const handleClick = (e: React.MouseEvent<HTMLLIElement>) => {
    e.stopPropagation();
    onClick?.();
  };

  return (
    <li
      className={cn(
        "text-xs flex items-center px-2 py-1 gap-1",
        "hover:bg-gray-100 cursor-pointer active:bg-gray-200"
      )}
      onClick={handleClick}
    >
      {children}
    </li>
  );
}

export function ItemContextMenu({
  itemId,
  position = "bottom",
}: {
  itemId: string;
  position?: Position;
}) {
  const hover = useTimelineContext(s => s.hover);
  const splitItem = useTimelineContext(s => s.splitItem);
  const deleteItem = useTimelineContext(s => s.deleteItem);
  const [frozenHover, setFrozenHover] = useState<HoverState | null>(null);
  const [onMenu, setOnMenu] = useState(false);

  useEffect(() => {
    if (onMenu) return;

    if (hover && hover.itemId !== itemId) {
      setFrozenHover(null);
      return;
    }

    const timeout = setTimeout(() => {
      setFrozenHover(hover);
    }, 400);

    return () => clearTimeout(timeout);
  }, [hover, itemId, onMenu]);

  useEffect(() => {
    if (!onMenu) {
      setFrozenHover(null);
    }
  }, [onMenu]);

  const handleSplit = () => {
    if (!frozenHover || !frozenHover.itemOffset) return;
    splitItem(itemId, frozenHover.itemOffset);
  };

  const handleDelete = () => {
    if (!frozenHover) return;
    deleteItem(itemId);
  };

  return (
    <div
      className={cn(
        "absolute translate-x-[-50%]",
        "rounded-lg bg-white border border-gray-300",
        "z-10 hidden",
        frozenHover !== null && "flex"
      )}
      style={{
        left: frozenHover?.itemOffsetX,
        width: 100,
        top: position === "bottom" ? "calc(100% + 6px)" : undefined,
        bottom: position === "top" ? "calc(100% + 6px)" : undefined,
      }}
      onMouseEnter={() => setOnMenu(true)}
      onMouseLeave={() => setOnMenu(false)}
    >
      <Arrow position={position} />
      <ul className="divide-y divide-gray-300 w-full rounded-[6px] overflow-hidden">
        <MenuItem onClick={handleSplit}>
          <TbScissors />
          Split
        </MenuItem>
        <MenuItem onClick={handleDelete}>
          <TbTrash />
          Delete
        </MenuItem>
      </ul>
    </div>
  );
}
