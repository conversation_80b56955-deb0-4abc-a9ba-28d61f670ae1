import cn from "@meltdownjs/cn";
import { useTimelineContext } from "./use-timeline-store";

export function Background() {
  const timelineSize = useTimelineContext(s => s.timelineSize);
  const zoom = useTimelineContext(s => s.zoom);
  const scale = useTimelineContext(s => s.scale);
  return (
    <div
      className={cn(
        "absolute top-0 left-0 bg-gray-50 h-[500px] flex overflow-hidden"
      )}
      style={{ width: `${timelineSize * zoom}px` }}
    >
      {scale.stripes.map(stripe => (
        <div
          key={stripe.at}
          className="h-full border-r border-gray-200"
          style={{
            width: `${scale.every * zoom}px`,
            minWidth: `${scale.every * zoom}px`,
            maxWidth: `${scale.every * zoom}px`,
          }}
        />
      ))}
    </div>
  );
}
