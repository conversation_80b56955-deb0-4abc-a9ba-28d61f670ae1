import { ScaledImage } from "./video-frame";

export type ExtractedFrameImage = ScaledImage & {
  timestamp: number;
};

export type ExtractedAudioBar = { value: number; frame: number; fps: number };

const DB_NAME = "thumbnail-cache";
const STORE_NAME = "thumbnail-cache";
const DB_VERSION = 1;

function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    request.onupgradeneeded = () => {
      const db = request.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME);
      }
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

async function getFromDB<T = unknown>(url: string): Promise<T | undefined> {
  const db = await openDB();
  return await new Promise((resolve, reject) => {
    const tx = db.transaction(STORE_NAME, "readonly");
    const store = tx.objectStore(STORE_NAME);
    const req = store.get(url);
    req.onsuccess = () => resolve(req.result as T | undefined);
    req.onerror = () => reject(req.error);
  });
}

async function setInDB<T = unknown>(url: string, payload: T): Promise<void> {
  const db = await openDB();
  return await new Promise((resolve, reject) => {
    const tx = db.transaction(STORE_NAME, "readwrite");
    const store = tx.objectStore(STORE_NAME);
    const req = store.put(payload, url);
    req.onsuccess = () => resolve();
    req.onerror = () => reject(req.error);
  });
}

export async function getFrameImages(
  url: string,
  reset: () => Promise<ExtractedFrameImage[]>
): Promise<ExtractedFrameImage[]> {
  const cached = await getFromDB<ExtractedFrameImage[]>(url);
  if (cached) {
    return cached;
  }
  const images = await reset();
  await setInDB<ExtractedFrameImage[]>(url, images);
  return images;
}

export async function getAudioBars(
  url: string,
  reset: () => Promise<ExtractedAudioBar[]>
): Promise<ExtractedAudioBar[]> {
  const cached = await getFromDB<ExtractedAudioBar[]>(url);
  if (cached) {
    return cached;
  }
  const bars = await reset();
  await setInDB<ExtractedAudioBar[]>(url, bars);
  return bars;
}

export async function getImage(
  url: string,
  reset: () => Promise<ScaledImage>
): Promise<ScaledImage> {
  const cached = await getFromDB<ScaledImage>(url);
  if (cached) {
    return cached;
  }
  const image = await reset();
  await setInDB<ScaledImage>(url, image);
  return image;
}
