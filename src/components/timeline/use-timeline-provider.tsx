import { PropsWithChildren, useRef } from "react";
import {
  createTimelineStore,
  TimelineContext,
  TimelineProps,
  TimelineStore,
} from "./use-timeline-store";

export function TimelineProvider({
  children,
  ...props
}: PropsWithChildren<
  Partial<TimelineProps> & {
    containerRef: React.RefObject<HTMLDivElement | null>;
  }
>) {
  const storeRef = useRef<TimelineStore>(null);
  if (!storeRef.current) {
    storeRef.current = createTimelineStore(props.containerRef, props);
  }

  return (
    <TimelineContext.Provider value={storeRef.current}>
      {children}
    </TimelineContext.Provider>
  );
}
