import { useEffect } from "react";
import { timeToStr } from "./use-timeline-store";
import { useDrag } from "./use-drag";
import cn from "@meltdownjs/cn";
import { useTimelineContext } from "./use-timeline-store";

export const SCALE_HEIGHT = 30;

export function Scale() {
  const timelineSize = useTimelineContext(s => s.timelineSize);
  const zoom = useTimelineContext(s => s.zoom);
  const scale = useTimelineContext(s => s.scale);
  const headAt = useTimelineContext(s => s.headAt);
  const updateHeadAt = useTimelineContext(s => s.updateHeadAt);
  const drag = useDrag<{ headAt: number }>();

  useEffect(() => {
    if (!drag.dragState) return;
    updateHeadAt((drag.dragState.offsetX + drag.dragState.deltaX) / zoom);
  }, [drag.dragState, updateHeadAt, zoom]);

  return (
    <div
      className={cn(
        "border-t border-b border-gray-200 flex",
        "overflow-hidden select-none"
      )}
      style={{ width: `${timelineSize * zoom}px`, height: SCALE_HEIGHT }}
      onMouseDown={drag.handleMouseDown("scale", { headAt })}
    >
      {scale.stripes.map(stripe => (
        <div
          key={stripe.at}
          className={cn(
            "h-full border-r border-gray-200 text-xs flex items-center px-1",
            "opacity-50"
          )}
          style={{
            width: `${scale.every * zoom}px`,
            minWidth: `${scale.every * zoom}px`,
            maxWidth: `${scale.every * zoom}px`,
          }}
        >
          {timeToStr(stripe.at)}
        </div>
      ))}
    </div>
  );
}
