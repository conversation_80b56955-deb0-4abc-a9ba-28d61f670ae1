import { createStore, useStore } from "zustand";
import { immer } from "zustand/middleware/immer";
import { DragState } from "./use-drag";
import { createContext, useContext } from "react";

export const SNAP_STEP = 0.01;
export const DEFAULT_TRACK_HEIGHT = 40;

export function lowerStep(value: number, step: number) {
  return value - (value % step);
}

export type TrackItem = {
  id: string;
  start: number;
  trimLeft: number;
  trimRight: number;
  totalSize: number;
  color?: string;
  left?: string;
  top?: string;
  text?: string;
  videoUrl?: string;
  audioUrl?: string;
  imageUrl?: string;
  width?: number;
  height?: number;
};

export type TrackType = "default" | "ordered";
export type MediaType =
  | "video"
  | "audio"
  | "narration"
  | "image"
  | "text"
  | "visual";

export type Track = {
  id: string;
  items: TrackItem[];
  type: TrackType;
  mediaType: MediaType;
  purpose: string;
  title: string;
  height?: number;
};

export type TrackItemDragState = {
  id: string;
  tracks: Track[];
  trackId: string;
};

export type HoverState = {
  clientX: number;
  clientY: number;
  trackId?: string;
  itemId?: string;
  itemOffsetX?: number;
  itemOffset?: number;
  trackOffsetX?: number;
  trackOffset?: number;
};

export const getItemSize = (item: TrackItem) => {
  return item.totalSize - item.trimLeft - item.trimRight;
};

export const getHover = (trackItems: TrackItem[], at: number) => {
  const leftItem = trackItems
    .filter(item => item.start < at)
    .sort((a, b) => b.start - a.start)[0];
  const rightItem = trackItems
    .filter(item => item.start >= at)
    .sort((a, b) => a.start - b.start)[0];
  const leftWall = leftItem ? leftItem.start + getItemSize(leftItem) : 0;
  const rightWall = rightItem ? rightItem.start : Infinity;
  const isVoid = at > leftWall && at < rightWall;
  const voidSize = isVoid ? rightWall - leftWall : 0;
  return { leftItem, rightItem, leftWall, rightWall, voidSize };
};

export const getTrackWidth = (track: Track) => {
  if (!track) return 0;
  if (track.items.length === 0) return 0;
  if (track.type === "ordered") {
    return track.items.reduce((acc, item) => {
      return acc + getItemSize(item);
    }, 0);
  }
  return Math.max(...track.items.map(item => item.start + getItemSize(item)));
};

export const timeToStr = (time: number) => {
  const twoDigits = (n: number) => n.toString().padStart(2, "0");
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = time % 60;
  return `${twoDigits(minutes)}:${twoDigits(seconds)}`;
};

export type Hover = ReturnType<typeof getHover>;

let id = 100;
export function getNextId() {
  return id++;
}

export const calcItemHeight = (trackHeight: number) => {
  const yPadding = 4;
  const border = 1;
  return trackHeight - yPadding * 2 - border * 2;
};

function adjustFocalLeft(
  scrollLeft: number,
  oldWidth: number,
  newWidth: number,
  focalLeft: number
): number {
  const deltaWidth = newWidth - oldWidth;
  const deltaScrollCenter = deltaWidth / 2;
  const center = oldWidth / 2;
  const deltaScroll = (focalLeft * deltaScrollCenter) / center;
  return scrollLeft + deltaScroll;
}

type TimelineScale = {
  stripes: { at: number }[];
  every: number;
};

type TimelineSelected = {
  item: TrackItem;
  trackIndex: number;
  itemIndex: number;
  track: Track;
  id: string;
};

export interface TimelineProps {
  tracks: Track[];
  zoom: number;
  headAt: number;
  refWidth: number;
  isBusy: boolean;
  selectedItemId: string | null;
  zoomBoundaries: { min: number; max: number };
  scale: TimelineScale;
  selected: TimelineSelected | null;
  pendingFocalPoint: number | null;
  hover: HoverState | null;
  timelineSize: number;
  size: number;
  dragState: DragState<TrackItemDragState> | null;

  // project level config. ideally should be in a separate store.
  aspectRatio: AspectRatio;
}

export interface TimelineState extends TimelineProps {
  setTracks: (tracks: Track[]) => void;
  addItem: (trackIndex: number, item: TrackItem) => void;
  setItem: (
    trackIndex: number,
    itemIndex: number,
    item: Partial<TrackItem>
  ) => void;
  getItemState: (id: string) => {
    moving: boolean;
    right: boolean;
    left: boolean;
    selected: boolean;
  };
  getItemStart: (trackIndex: number, itemIndex: number) => number;
  isHeadOnItem: (id: string) => boolean;
  splitItem: (id: string, at: number) => void;
  deleteItem: (id: string) => void;
  updateHeadAt: (at: number) => void;
  updateHeadAtExternal: (at: number) => void;
  centerHead: (smooth?: boolean) => void;
  focusHead: (forced?: boolean) => void;
  fitZoom: () => void;
  setZoom: (zoom: number) => void;
  setZoomWithFocalPoint: (newZoom: number) => void;
  setSelectedItemId: (id: string | null) => void;
  getTrackHeight: (trackIndex: number) => number;
  setRefWidth: (width: number) => void;
  setHover: (hover: HoverState | null) => void;
  setDragState: (dragState: DragState<TrackItemDragState> | null) => void;
  setIsBusy: (isBusy: boolean) => void;
  setTimelineSize: (timelineSize: number) => void;
  setSize: (size: number) => void;
  setScale: (scale: TimelineScale) => void;
  setSelected: (selected: TimelineSelected | null) => void;
  setAspectRatio: (aspectRatio: AspectRatio) => void;
}

export type AspectRatio = "16:9" | "9:16" | "1:1";

export function createTimelineStore(
  containerRef: React.RefObject<HTMLDivElement | null>,
  initProps?: Partial<TimelineProps>
) {
  const defaultProps: TimelineProps = {
    tracks: [],
    zoom: 1,
    headAt: 0,
    refWidth: 0,
    isBusy: false,
    selectedItemId: null,
    zoomBoundaries: { min: 1, max: 20 },
    scale: { stripes: [], every: 1 },
    selected: null,
    pendingFocalPoint: null,
    hover: null,
    timelineSize: 0,
    size: 0,
    dragState: null,

    aspectRatio: "16:9",
  };

  return createStore<TimelineState>()(
    immer((set, get) => {
      return {
        ...defaultProps,
        ...initProps,

        setTracks: tracks =>
          set(state => {
            state.tracks = tracks;
          }),
        addItem: (trackIndex, item) =>
          set(state => {
            const track = state.tracks[trackIndex];
            if (!track) return;
            track.items.push(item);
          }),
        setItem: (trackIndex, itemIndex, item) =>
          set(state => {
            const track = state.tracks[trackIndex];
            if (!track) return;
            track.items[itemIndex] = {
              ...track.items[itemIndex],
              ...item,
            };
          }),
        getItemState: id => {
          const dragState = get().dragState;
          return {
            moving: dragState?.type === "move" && dragState.state.id === id,
            right: dragState?.type === "right" && dragState.state.id === id,
            left: dragState?.type === "left" && dragState.state.id === id,
            selected: get().selectedItemId === id,
          };
        },
        getItemStart: (trackIndex, itemIndex) => {
          const tracks = get().tracks;
          const track = tracks[trackIndex];
          if (!track) return 0;
          if (track.type === "ordered") {
            return track.items.slice(0, itemIndex).reduce((acc, item) => {
              return acc + getItemSize(item);
            }, 0);
          }
          return track.items[itemIndex].start;
        },
        isHeadOnItem: id => {
          const tracks = get().tracks;
          const headAt = get().headAt;
          for (let i = 0; i < tracks.length; i++) {
            const track = tracks[i];
            const itemIndex = track.items.findIndex(item => item.id === id);
            if (itemIndex === -1) continue;
            const item = track.items[itemIndex];
            const start = get().getItemStart(i, itemIndex);
            return headAt >= start && headAt < start + getItemSize(item);
          }
          return false;
        },
        splitItem: (id, at) =>
          set(state => {
            for (const track of state.tracks) {
              const itemIndex = track.items.findIndex(item => item.id === id);
              if (itemIndex === -1) continue;
              const item = track.items[itemIndex];
              const newItem = {
                ...item,
                trimLeft: item.trimLeft + at,
                start: item.start + at,
                id: getNextId().toString(),
              };
              item.trimRight += getItemSize(item) - at;
              track.items.splice(itemIndex + 1, 0, newItem);
            }
          }),
        deleteItem: id =>
          set(state => {
            if (state.selectedItemId === id) {
              state.selectedItemId = null;
            }
            for (const track of state.tracks) {
              const itemIndex = track.items.findIndex(item => item.id === id);
              if (itemIndex === -1) continue;
              track.items.splice(itemIndex, 1);
            }
          }),
        updateHeadAt: at =>
          set(state => {
            state.headAt = lowerStep(at, SNAP_STEP);
            if (containerRef.current) {
              containerRef.current.dispatchEvent(
                new CustomEvent("headupdated", {
                  detail: { at },
                })
              );
            }
          }),
        updateHeadAtExternal: at =>
          set(state => {
            state.headAt = lowerStep(at, SNAP_STEP);
          }),
        centerHead: (smooth = false) => {
          const headAt = get().headAt;
          const zoom = get().zoom;
          if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            const width = rect.width;
            const left = headAt * zoom - width / 2;
            containerRef.current.scrollTo({
              left,
              behavior: smooth ? "smooth" : "instant",
            });
          }
        },
        focusHead: (forced = false) => {
          const headAt = get().headAt;
          const zoom = get().zoom;
          if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            const start = containerRef.current.scrollLeft / zoom;
            const end = start + rect.width / zoom;
            const leftDelta = start - headAt;
            const rightDelta = headAt - end;
            const maxDelta = Math.max(leftDelta, rightDelta);
            const shouldCenter = maxDelta > -1 && maxDelta < 1;
            if (shouldCenter || forced) {
              get().centerHead(!forced);
            }
          }
        },
        fitZoom: () => {
          const size = get().size;
          if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            const width = rect.width;
            set(state => {
              state.zoom = width / size;
            });
          }
        },
        setZoom: zoom =>
          set(state => {
            state.zoom = zoom;
          }),
        setZoomWithFocalPoint: newZoom => {
          const timelineSize = get().timelineSize;
          const zoom = get().zoom;
          const headAt = get().headAt;
          if (!containerRef.current) return;
          const container = containerRef.current;
          const oldWidth = timelineSize * zoom;
          const scrollLeft = container.scrollLeft;
          const headX = headAt * zoom;
          const newWidth = timelineSize * newZoom;
          set(state => {
            state.pendingFocalPoint = adjustFocalLeft(
              scrollLeft,
              oldWidth,
              newWidth,
              headX
            );
            state.zoom = newZoom;
          });
        },
        setSelectedItemId: id =>
          set(state => {
            state.selectedItemId = id;
          }),
        getTrackHeight: trackIndex => {
          const tracks = get().tracks;
          const track = tracks[trackIndex];
          if (!track) return 0;
          return track.height ?? DEFAULT_TRACK_HEIGHT;
        },
        setRefWidth: width =>
          set(state => {
            state.refWidth = width;
          }),
        setHover: hover =>
          set(state => {
            state.hover = hover;
          }),
        setDragState: dragState =>
          set(state => {
            state.dragState = dragState;
          }),
        setIsBusy: isBusy =>
          set(state => {
            state.isBusy = isBusy;
          }),
        setTimelineSize: timelineSize =>
          set(state => {
            state.timelineSize = timelineSize;
          }),
        setSize: size =>
          set(state => {
            state.size = size;
          }),
        setScale: scale =>
          set(state => {
            state.scale = scale;
          }),
        setSelected: selected =>
          set(state => {
            state.selected = selected;
          }),
        setAspectRatio: aspectRatio =>
          set(state => {
            state.aspectRatio = aspectRatio;
          }),
      };
    })
  );
}

export type TimelineStore = ReturnType<typeof createTimelineStore>;

export const TimelineContext = createContext<TimelineStore | null>(null);

export function useTimelineContext<T>(
  selector: (state: TimelineState) => T
): T {
  const store = useContext(TimelineContext);
  if (!store) throw new Error("Missing TimelineContext.Provider in the tree");
  return useStore(store, selector);
}
