import { useCallback, useEffect, useRef, useState } from "react";

export type DragType = "move" | "right" | "left" | "scale";

export type DragState<T> = {
  offsetX: number;
  deltaX: number;
  startX: number;
  clientX: number;
  clientY: number;
  state: T;
  type: DragType;
};

export const useDrag = <T>() => {
  const [dragState, setDragState] = useState<DragState<T> | null>(null);
  const prevCursor = useRef<CSSStyleDeclaration["cursor"] | null>(null);

  const handleMouseUp = useCallback(() => {
    setDragState(null);
    if (prevCursor.current) {
      document.body.style.cursor = prevCursor.current;
    }
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!dragState) return;

      e.preventDefault();
      e.stopPropagation();

      setDragState({
        ...dragState,
        deltaX: e.clientX - dragState.startX,
        clientX: e.clientX,
        clientY: e.clientY,
      });
    },
    [dragState]
  );

  useEffect(() => {
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("mousemove", handleMouseMove);
    return () => {
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [handleMouseUp, handleMouseMove]);

  const handleMouseDown = useCallback(
    (type: DragType, state: T, cursor?: CSSStyleDeclaration["cursor"]) =>
      (e: React.MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();

        if (cursor) {
          prevCursor.current = document.body.style.cursor || "default";
          document.body.style.cursor = cursor;
        }

        const rect = e.currentTarget.getBoundingClientRect();
        setDragState({
          offsetX: e.clientX - rect.left,
          deltaX: 0,
          startX: e.clientX,
          clientX: e.clientX,
          clientY: e.clientY,
          type,
          state,
        });
      },
    []
  );

  return { handleMouseDown, dragState };
};
