import { useEffect, useState } from "react";
import { getHover, HoverState, useTimelineContext } from "./use-timeline-store";
import { TbTrash } from "react-icons/tb";
import cn from "@meltdownjs/cn";

export function TrackHoverMenu({ trackId }: { trackId: string }) {
  const hover = useTimelineContext(s => s.hover);
  const tracks = useTimelineContext(s => s.tracks);
  const [frozenHover, setFrozenHover] = useState<HoverState | null>(null);

  useEffect(() => {
    if (hover?.trackId !== trackId) {
      setFrozenHover(null);
      return;
    }

    const timeout = setTimeout(() => {
      const track = tracks.find(track => track.id === trackId);

      if (!hover || !track || !hover.trackOffset) return;

      const drop = getHover(track.items, hover.trackOffset);
      if (drop.voidSize > 0 && drop.leftItem && drop.rightItem) {
        setFrozenHover(hover);
      }
    }, 1000);

    return () => clearTimeout(timeout);
  }, [hover, tracks, trackId]);

  return (
    <div
      className={cn(
        "absolute z-10 top-1/2 -translate-y-1/2 hidden -translate-x-1/2",
        "bg-gray-200 text-gray-500 rounded p-1 cursor-pointer",
        "hover:bg-gray-300 hover:text-gray-600",
        frozenHover !== null && "flex"
      )}
      style={{
        left: frozenHover?.trackOffsetX,
      }}
    >
      <TbTrash />
    </div>
  );
}
