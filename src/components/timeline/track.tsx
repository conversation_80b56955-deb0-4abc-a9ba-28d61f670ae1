import { useMemo } from "react";
import { Renderer, TrackItem } from "./track-item";
import cn from "@meltdownjs/cn";
import { TrackHoverMenu } from "./track-hover-menu";
import { useTimelineContext } from "./use-timeline-store";

export function Track({
  trackIndex,
  renderer,
}: {
  trackIndex: number;
  renderer: Renderer;
}) {
  const tracks = useTimelineContext(s => s.tracks);
  const timelineSize = useTimelineContext(s => s.timelineSize);
  const zoom = useTimelineContext(s => s.zoom);
  const getTrackHeight = useTimelineContext(s => s.getTrackHeight);
  const track = useMemo(() => tracks[trackIndex], [trackIndex, tracks]);

  return (
    <div
      data-track-id={track.id}
      className="py-1 timeline-track relative"
      style={{ height: getTrackHeight(trackIndex) }}
    >
      <div
        className={cn(
          "flex items-center h-full",
          track.type === "default" && "relative"
        )}
        style={{ width: `${timelineSize * zoom}px` }}
      >
        {track.items.map(item => (
          <TrackItem
            key={item.id}
            id={item.id}
            trackIndex={trackIndex}
            renderer={renderer}
          />
        ))}
      </div>

      <TrackHoverMenu trackId={track.id} />
    </div>
  );
}
