import { PropsWithChildren, useEffect, useMemo } from "react";
import {
  getItemSize,
  Track,
  TrackItemDragState,
  TrackItem as TrackItemType,
} from "./use-timeline-store";
import cn from "@meltdownjs/cn";
import { TbGripVertical } from "react-icons/tb";
import { ItemContextMenu } from "./item-hover-menu";
import { useTimelineContext } from "./use-timeline-store";
import { useDrag } from "./use-drag";

export type Renderer = (args: {
  trackIndex: number;
  trackItemIndex: number;
  trackItem: TrackItemType;
  track: Track;
}) => React.ReactNode;

function TrimHandle({
  onMouseDown,
  grabbingAny,
  side,
  grabbing,
}: {
  onMouseDown: (e: React.MouseEvent<HTMLDivElement>) => void;
  grabbingAny: boolean;
  grabbing: boolean;
  side: "left" | "right";
}) {
  return (
    <div
      className={cn(
        "w-2 h-[80%] my-auto cursor-ew-resize absolute top-[10%] mx-1 rounded-full",
        "bg-white opacity-0 group-hover:opacity-60 transition-opacity",
        "flex justify-center items-center",
        side === "left" && "left-0",
        side === "right" && "right-0",
        grabbingAny && !grabbing && "opacity-60",
        grabbing && "opacity-80 group-hover:opacity-80"
      )}
      onMouseDown={onMouseDown}
    >
      <TbGripVertical />
    </div>
  );
}

function TrackItemContent({
  children,
  selected,
  moving,
  trackItem,
  onMouseDown,
}: PropsWithChildren<{
  selected: boolean;
  moving: boolean;
  trackItem: TrackItemType;
  onMouseDown: (e: React.MouseEvent<HTMLDivElement>) => void;
}>) {
  return (
    <div
      className={cn(
        "flex-1 flex items-center justify-center text-xs",
        "overflow-hidden rounded-lg border border-gray-300",
        selected && "border-orange-400",
        moving && "bg-blue-100"
      )}
      style={{
        background: trackItem.color,
      }}
      onMouseDown={onMouseDown}
    >
      {children}
    </div>
  );
}

export function TrackTypeBadge({ children }: PropsWithChildren) {
  return (
    <div
      className={cn(
        "bg-blue-100 text-blue-800 absolute bottom-[2px] left-[2px]",
        "px-[2px] py-[0px] rounded-xs leading-0 font-bold text-[7px]",
        "border border-blue-200"
      )}
    >
      {children}
    </div>
  );
}

export function TrackItem({
  trackIndex,
  id,
  renderer,
}: {
  trackIndex: number;
  id: string;
  renderer: Renderer;
}) {
  const tracks = useTimelineContext(s => s.tracks);
  const zoom = useTimelineContext(s => s.zoom);
  const setSelectedItemId = useTimelineContext(s => s.setSelectedItemId);
  const getItemState = useTimelineContext(s => s.getItemState);
  const setDragState = useTimelineContext(s => s.setDragState);

  const dragLocal = useDrag<TrackItemDragState>();

  const track = useMemo(() => tracks[trackIndex], [trackIndex, tracks]);
  const trackItem = useMemo(
    () => track.items.find(item => item.id === id)!,
    [id, track]
  );
  const view = useMemo(
    () =>
      renderer({
        trackIndex,
        trackItemIndex: track.items.indexOf(trackItem),
        trackItem,
        track,
      }),
    [renderer, trackIndex, trackItem, track]
  );

  useEffect(() => {
    setDragState(dragLocal.dragState);
  }, [dragLocal, setDragState]);

  function handleClick() {
    setSelectedItemId(trackItem.id);
  }

  const { moving, right, left, selected } = getItemState(id);
  const size = getItemSize(trackItem);

  return (
    <div
      className={cn(
        "h-full bg-white flex group relative",
        "timeline-track-item",
        track.type === "default" && "absolute"
      )}
      style={{
        width: `${size * zoom}px`,
        left:
          track.type === "default" ? `${trackItem.start * zoom}px` : undefined,
        minWidth: `${size * zoom}px`,
        maxWidth: `${size * zoom}px`,
      }}
      onClick={handleClick}
      data-track-item-id={id}
    >
      <TrackItemContent
        selected={selected}
        moving={moving}
        trackItem={trackItem}
        onMouseDown={dragLocal.handleMouseDown(
          "move",
          {
            tracks,
            id,
            trackId: track.id,
          },
          "grabbing"
        )}
      >
        {view}
      </TrackItemContent>
      <TrimHandle
        onMouseDown={dragLocal.handleMouseDown(
          "left",
          {
            tracks,
            id,
            trackId: track.id,
          },
          "ew-resize"
        )}
        grabbingAny={left || right}
        grabbing={left}
        side="left"
      />
      <TrimHandle
        onMouseDown={dragLocal.handleMouseDown(
          "right",
          {
            tracks,
            id,
            trackId: track.id,
          },
          "ew-resize"
        )}
        grabbingAny={left || right}
        grabbing={right}
        side="right"
      />

      <ItemContextMenu
        itemId={id}
        position={trackIndex < tracks.length / 2 ? "bottom" : "top"}
      />
    </div>
  );
}
