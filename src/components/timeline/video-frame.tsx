export type ScaledImage = {
  b64: string;
  scale: number;
  width: number;
  height: number;
};

export function videoFrameToImage(
  frame: VideoFrame,
  maxHeight: number
): ScaledImage {
  const canvas = document.createElement("canvas");
  canvas.width = frame.codedWidth;
  canvas.height = frame.codedHeight;

  const scale = maxHeight / frame.codedHeight;

  const width = frame.codedWidth * scale;
  const height = frame.codedHeight * scale;

  canvas.width = width;
  canvas.height = height;

  const ctx = canvas.getContext("2d");
  ctx?.drawImage(frame, 0, 0, canvas.width, canvas.height);
  const b64 = canvas.toDataURL();

  return { b64, scale, width, height };
}
