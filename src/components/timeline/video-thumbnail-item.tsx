import { useEffect, useMemo, useRef, useState } from "react";
import { extractFrames } from "@remotion/webcodecs";
import { videoFrameToImage } from "./video-frame";
import { ExtractedFrameImage, getFrameImages } from "./item-media-storage";
import { useDebounceEffect } from "./use-debounce-effect";
import { drawB64Image } from "./canvas-draw";
import {
  calcItemHeight,
  TrackItem,
  useTimelineContext,
} from "./use-timeline-store";

const SCALE_FACTOR = 2;
const TOTAL_FRAMES = 50;

function extractFrameImages(
  url: string,
  duration: number
): Promise<ExtractedFrameImage[]> {
  return new Promise(resolve => {
    const images: ExtractedFrameImage[] = [];
    extractFrames({
      src: url,
      timestampsInSeconds: Array.from(
        { length: TOTAL_FRAMES },
        (_, i) => i * (duration / TOTAL_FRAMES)
      ),
      onFrame: frame => {
        images.push({
          ...videoFrameToImage(frame, 100),
          timestamp: frame.timestamp,
        });
        if (images.length === TOTAL_FRAMES) {
          resolve(images);
        }
        frame.close();
      },
    });
  });
}

export function VideoThumbnailItem({
  item,
  trackHeight,
}: {
  item: TrackItem;
  trackHeight: number;
}) {
  const zoom = useTimelineContext(s => s.zoom);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const dimensions = useMemo(() => {
    const itemHeight = calcItemHeight(trackHeight);
    const videoWidth = 1920;
    const videoHeight = 1080;
    const thumbnailHeight = itemHeight;
    const thumbnailWidth = (thumbnailHeight * videoWidth) / videoHeight;
    return {
      thumbnailWidth,
      thumbnailHeight,
      itemHeight,
      videoWidth,
      videoHeight,
    };
  }, [trackHeight]);

  const [images, setImages] = useState<ExtractedFrameImage[]>([]);

  useEffect(() => {
    (async () => {
      if (!item.videoUrl) return;

      setImages(
        await getFrameImages(item.videoUrl, () =>
          extractFrameImages(item.videoUrl!, item.totalSize)
        )
      );
    })();
  }, [item.totalSize, item.id, item.videoUrl]);

  useDebounceEffect(
    () => {
      (async () => {
        const sortedImages = images.sort((a, b) => a.timestamp - b.timestamp);
        const visibleCanvas = canvasRef.current;
        if (!visibleCanvas) return;
        const ctx = visibleCanvas.getContext("2d");
        if (!ctx) return;

        const width = item.totalSize * zoom * SCALE_FACTOR;
        const height = dimensions.itemHeight * SCALE_FACTOR;

        const offscreen = document.createElement("canvas");
        offscreen.width = width;
        offscreen.height = height;
        const offCtx = offscreen.getContext("2d");
        if (!offCtx) return;

        let dx = 0;
        while (dx < item.totalSize * zoom) {
          const image = sortedImages.find(
            image => image.timestamp / 1e6 >= dx / zoom
          );
          if (image) {
            await drawB64Image(
              offCtx,
              image.b64,
              dx * SCALE_FACTOR,
              0,
              dimensions.thumbnailWidth * SCALE_FACTOR,
              dimensions.thumbnailHeight * SCALE_FACTOR
            );
          }
          dx += dimensions.thumbnailWidth;
        }

        visibleCanvas.width = width;
        visibleCanvas.height = height;
        ctx.drawImage(offscreen, 0, 0);

        visibleCanvas.style.width = `${item.totalSize * zoom}px`;
        visibleCanvas.style.height = `${dimensions.itemHeight}px`;
      })();
    },
    50,
    [images, zoom, dimensions, item.totalSize]
  );

  return (
    <div className="w-full overflow-hidden">
      <canvas
        ref={canvasRef}
        style={{
          transform: `translateX(-${item.trimLeft * zoom}px)`,
        }}
      />
    </div>
  );
}
