import { useEffect } from "react";
import {
  getTrackWidth,
  HoverState,
  useTimelineContext,
} from "./use-timeline-store";
import { checkIfBusy } from "./busy-checker";

export function useTimelineHooks(
  containerRef: React.RefObject<HTMLDivElement | null>
) {
  const timelineSize = useTimelineContext(s => s.timelineSize);
  const setRefWidth = useTimelineContext(s => s.setRefWidth);
  const setZoom = useTimelineContext(s => s.setZoom);
  const setSelectedItemId = useTimelineContext(s => s.setSelectedItemId);
  const zoom = useTimelineContext(s => s.zoom);
  const tracks = useTimelineContext(s => s.tracks);
  const size = useTimelineContext(s => s.size);
  const headAt = useTimelineContext(s => s.headAt);
  const setHover = useTimelineContext(s => s.setHover);
  const drag = useTimelineContext(s => s.dragState);
  const setIsBusy = useTimelineContext(s => s.setIsBusy);
  const setTimelineSize = useTimelineContext(s => s.setTimelineSize);
  const refWidth = useTimelineContext(s => s.refWidth);
  const setSize = useTimelineContext(s => s.setSize);
  const setScale = useTimelineContext(s => s.setScale);
  const setSelected = useTimelineContext(s => s.setSelected);
  const selectedItemId = useTimelineContext(s => s.selectedItemId);

  // update ref width on resize
  useEffect(() => {
    setRefWidth(containerRef.current?.getBoundingClientRect().width ?? 0);

    const handleResize = () => {
      setRefWidth(containerRef.current?.getBoundingClientRect().width ?? 0);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [containerRef, setRefWidth]);

  // init zoom
  useEffect(() => {
    setTimeout(() => setZoom(10), 10);
  }, [setZoom]);

  // deselect item when clicking outside of a track item
  useEffect(() => {
    const isChildOf = (target: HTMLElement, className: string) => {
      if (target.classList.contains(className)) return true;
      if (target.parentElement)
        return isChildOf(target.parentElement, className);
      return false;
    };

    const handleClick = (e: MouseEvent) => {
      if (!isChildOf(e.target as HTMLElement, "timeline-track-item")) {
        setSelectedItemId(null);
      }
    };

    window.addEventListener("click", handleClick);
    return () => window.removeEventListener("click", handleClick);
  }, [setSelectedItemId]);

  useEffect(() => {
    checkIfBusy(setIsBusy);
  }, [zoom, tracks, size, headAt, setIsBusy]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (drag) {
        setHover(null);
        return;
      }

      const hover: HoverState = {
        clientX: e.clientX,
        clientY: e.clientY,
      };
      const tracks = document.querySelectorAll(".timeline-track");
      for (const track of tracks) {
        const trackRect = track.getBoundingClientRect();
        if (
          e.clientX > trackRect.left &&
          e.clientX < trackRect.right &&
          e.clientY > trackRect.top &&
          e.clientY < trackRect.bottom
        ) {
          const trackId = track.getAttribute("data-track-id")!;
          hover.trackId = trackId;
          hover.trackOffsetX = e.clientX - trackRect.left;
          hover.trackOffset = hover.trackOffsetX / zoom;

          const trackItems = track.querySelectorAll(".timeline-track-item");
          for (const trackItem of trackItems) {
            const trackItemRect = trackItem.getBoundingClientRect();
            if (
              e.clientX > trackItemRect.left &&
              e.clientX < trackItemRect.right &&
              e.clientY > trackItemRect.top &&
              e.clientY < trackItemRect.bottom
            ) {
              const itemId = trackItem.getAttribute("data-track-item-id")!;
              const offsetX = e.clientX - trackItemRect.left;

              hover.trackId = trackId;
              hover.itemId = itemId;
              hover.itemOffsetX = offsetX;
              hover.itemOffset = offsetX / zoom;

              break;
            }
          }
        }
      }
      setHover(hover);
    };
    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [drag, zoom, setHover]);

  useEffect(() => {
    const max = Math.max(...tracks.map(track => getTrackWidth(track)));
    const viewSize = refWidth / zoom;
    setTimelineSize(Math.max(max, viewSize));
  }, [tracks, zoom, refWidth, setTimelineSize]);

  useEffect(() => {
    const max = Math.max(...tracks.map(track => getTrackWidth(track)));
    setSize(max);
  }, [tracks, setSize]);

  useEffect(() => {
    const minWidth = 60;
    let every = 0.5;
    while (every * zoom < minWidth) {
      every += 0.5;
    }

    const stripes = [];
    for (let i = 0; i < timelineSize; i += every) {
      stripes.push({
        at: i,
      });
    }
    setScale({ stripes, every });
  }, [zoom, setScale, timelineSize]);

  useEffect(() => {
    for (let i = 0; i < tracks.length; i++) {
      for (let j = 0; j < tracks[i].items.length; j++) {
        const item = tracks[i].items[j];
        if (item.id === selectedItemId)
          return setSelected({
            item,
            trackIndex: i,
            itemIndex: j,
            track: tracks[i],
            id: selectedItemId,
          });
      }
    }
    setSelected(null);
  }, [tracks, selectedItemId, setSelected]);
}
