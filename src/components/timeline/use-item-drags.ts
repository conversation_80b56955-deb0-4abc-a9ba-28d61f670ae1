import { useEffect } from "react";
import { DragState } from "./use-drag";
import { clamp } from "./math";
import {
  getHover,
  lowerStep,
  SNAP_STEP,
  Track,
  TrackItemDragState,
  TrackItem as TrackItemType,
  useTimelineContext,
} from "./use-timeline-store";
import { getItemSize } from "./use-timeline-store";

function moveItems(items: TrackItemType[], fromIndex: number, toIndex: number) {
  if (fromIndex > toIndex) {
    return [
      ...items.slice(0, toIndex),
      items[fromIndex],
      ...items.slice(toIndex, fromIndex),
      ...items.slice(fromIndex + 1),
    ];
  }
  return [
    ...items.slice(0, fromIndex),
    ...items.slice(fromIndex + 1, toIndex + 1),
    items[fromIndex],
    ...items.slice(toIndex + 1),
  ];
}

function getOnIndex(items: TrackItemType[], point: number) {
  let onIndex = -1;
  let lastEnd = 0;
  for (let i = 0; i < items.length; i++) {
    if (lastEnd > point) {
      break;
    }
    lastEnd += getItemSize(items[i]);
    onIndex = i;
  }
  return onIndex;
}

function getOnTrackIndex(yPx: number) {
  const timeline = document.querySelectorAll(".timeline-track");
  for (let i = 0; i < timeline.length; i++) {
    const rect = timeline[i].getBoundingClientRect();
    if (yPx >= rect.top && yPx <= rect.bottom) {
      return i;
    }
  }
  return -1;
}

export function useMoveDrag(dragState: DragState<TrackItemDragState> | null) {
  const zoom = useTimelineContext(s => s.zoom);
  const setItem = useTimelineContext(s => s.setItem);
  const setTracks = useTimelineContext(s => s.setTracks);
  const setSelectedItemId = useTimelineContext(s => s.setSelectedItemId);

  useEffect(() => {
    // base case
    if (!dragState || dragState.type !== "move") return;

    const { tracks, id, trackId } = dragState.state;
    const trackIndex = tracks.findIndex(track => track.id === trackId);
    if (trackIndex === -1) {
      return;
    }

    // get the track dragging on
    const onTrackIndex = getOnTrackIndex(dragState.clientY);
    if (onTrackIndex === -1) {
      return;
    }

    const draft: Track[] = JSON.parse(JSON.stringify(tracks));

    // move the item to the new track
    if (
      onTrackIndex !== trackIndex &&
      draft[onTrackIndex].mediaType === draft[trackIndex].mediaType &&
      draft[onTrackIndex].purpose === draft[trackIndex].purpose
    ) {
      const itemIndex = draft[trackIndex].items.findIndex(
        item => item.id === id
      );
      const [removed] = draft[trackIndex].items.splice(itemIndex, 1);
      draft[onTrackIndex].items.push(removed);
    }

    // get the item and track
    const track = draft[onTrackIndex];
    const itemIndex = track.items.findIndex(item => item.id === id);
    const trackItem = track.items[itemIndex];
    if (!trackItem) return;

    // calculate the delta and offset
    const delta = lowerStep(dragState.deltaX / zoom, SNAP_STEP);
    const offset = dragState.offsetX / zoom;

    if (track.type === "default") {
      const start = trackItem.start + delta;
      const size = getItemSize(trackItem);

      const hover = getHover(
        track.items.filter(item => item.id !== trackItem.id),
        start + offset
      );
      if (hover.rightWall - hover.leftWall < size) {
        return;
      }

      // update the item
      draft[onTrackIndex].items[itemIndex] = {
        ...draft[onTrackIndex].items[itemIndex],
        start: clamp(start, hover.leftWall, hover.rightWall - size),
      };
    } else if (track.type === "ordered") {
      const start = track.items
        .slice(0, itemIndex)
        .reduce((acc, item) => acc + getItemSize(item), 0);
      const point = offset + start + delta;

      // get the drop index
      const onIndex = getOnIndex(track.items, point);
      if (onIndex === -1) {
        return;
      }

      // move the item to the new index
      draft[onTrackIndex].items = moveItems(track.items, itemIndex, onIndex);
    }

    setSelectedItemId(id);
    setTracks(draft);
  }, [dragState, zoom, setItem, setTracks, setSelectedItemId]);
}

export function useRightDrag(dragState: DragState<TrackItemDragState> | null) {
  const zoom = useTimelineContext(s => s.zoom);
  const setItem = useTimelineContext(s => s.setItem);
  const setSelectedItemId = useTimelineContext(s => s.setSelectedItemId);

  useEffect(() => {
    // base case
    if (!dragState || dragState.type !== "right") return;

    const { id, tracks, trackId } = dragState.state;
    const trackIndex = tracks.findIndex(track => track.id === trackId);
    if (trackIndex === -1) {
      return;
    }

    // get the item and track
    const itemIndex = tracks[trackIndex].items.findIndex(
      item => item.id === id
    );
    const trackItem = tracks[trackIndex].items[itemIndex];
    const track = tracks[trackIndex];

    const delta = lowerStep(dragState.deltaX / zoom, SNAP_STEP);
    const draft: Partial<TrackItemType> = {};

    const hover = getHover(
      track.items.filter(item => item.id !== trackItem.id),
      trackItem.start
    );

    let minTrackRight = 0;
    if (track.type === "default") {
      minTrackRight = Math.max(
        0,
        trackItem.start +
          trackItem.totalSize -
          trackItem.trimLeft -
          hover.rightWall
      );
    }

    const trimRight = clamp(
      trackItem.trimRight - delta,
      minTrackRight,
      trackItem.totalSize - trackItem.trimLeft
    );

    draft.trimRight = trimRight;

    setSelectedItemId(id);
    setItem(trackIndex, itemIndex, draft);
  }, [dragState, zoom, setItem, setSelectedItemId]);
}

export function useLeftDrag(dragState: DragState<TrackItemDragState> | null) {
  const zoom = useTimelineContext(s => s.zoom);
  const setItem = useTimelineContext(s => s.setItem);
  const setSelectedItemId = useTimelineContext(s => s.setSelectedItemId);

  useEffect(() => {
    // base case
    if (!dragState || dragState.type !== "left") return;

    const { id, tracks, trackId } = dragState.state;
    const trackIndex = tracks.findIndex(track => track.id === trackId);
    if (trackIndex === -1) {
      return;
    }

    // get the item and track
    const itemIndex = tracks[trackIndex].items.findIndex(
      item => item.id === id
    );
    const trackItem = tracks[trackIndex].items[itemIndex];
    const track = tracks[trackIndex];

    const delta = lowerStep(dragState.deltaX / zoom, SNAP_STEP);
    const draft: Partial<TrackItemType> = {};

    const hover = getHover(
      track.items.filter(item => item.id !== trackItem.id),
      trackItem.start
    );

    let minTrimLeft = 0;
    if (track.type === "default") {
      minTrimLeft = Math.max(
        0,
        hover.leftWall - (trackItem.start - trackItem.trimLeft)
      );
    }

    const trimLeft = clamp(
      trackItem.trimLeft + delta,
      minTrimLeft,
      trackItem.totalSize - trackItem.trimRight
    );

    draft.trimLeft = trimLeft;
    draft.start = trackItem.start - (trackItem.trimLeft - trimLeft);

    setSelectedItemId(trackItem.id);
    setItem(trackIndex, itemIndex, draft);
  }, [dragState, zoom, setItem, setSelectedItemId]);
}
