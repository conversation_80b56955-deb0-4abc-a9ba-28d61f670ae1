import { useEffect, useMemo, useRef, useState } from "react";
import { calcItemHeight, TrackItem } from "./use-timeline-store";
import { getAudioData, visualizeAudio } from "@remotion/media-utils";
import { ExtractedAudioBar, getAudioBars } from "./item-media-storage";
import { useDebounceEffect } from "./use-debounce-effect";
import { useTimelineContext } from "./use-timeline-store";

async function extractWaveform(
  url: string,
  totalSize: number
): Promise<ExtractedAudioBar[]> {
  const audioData = await getAudioData(url);

  const fps = 200;
  const totalFrames = Math.floor(totalSize * fps);
  const bars: ExtractedAudioBar[] = [];
  for (let frame = 0; frame < totalFrames; frame++) {
    const waveform = visualizeAudio({
      fps,
      frame,
      audioData,
      numberOfSamples: 1,
    });
    bars.push({ value: waveform[0], frame, fps });
  }
  return bars;
}

export function AudioItem({
  item,
  trackHeight,
}: {
  item: TrackItem;
  trackHeight: number;
}) {
  const zoom = useTimelineContext(s => s.zoom);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [bars, setBars] = useState<ExtractedAudioBar[]>([]);

  const dimensions = useMemo(() => {
    return {
      itemHeight: calcItemHeight(trackHeight),
      barWidth: 1,
    };
  }, [trackHeight]);

  useEffect(() => {
    (async () => {
      setBars(
        await getAudioBars(item.audioUrl!, () =>
          extractWaveform(item.audioUrl!, item.totalSize)
        )
      );
    })();
  }, [item.audioUrl, item.totalSize]);

  useDebounceEffect(
    () => {
      (async () => {
        const ctx = canvasRef.current?.getContext("2d");
        if (!ctx || !canvasRef.current) return;

        canvasRef.current.width = item.totalSize * zoom;
        canvasRef.current.height = dimensions.itemHeight;

        canvasRef.current.style.width = `${item.totalSize * zoom}px`;
        canvasRef.current.style.height = `${dimensions.itemHeight}px`;

        ctx.fillStyle = "rgba(255, 255, 255, 0.5)";
        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);

        const { itemHeight, barWidth } = dimensions;

        for (let i = 0; i < zoom * item.totalSize; i++) {
          const bar = bars.find(bar => bar.frame / bar.fps >= i / zoom);
          if (!bar) continue;
          ctx.fillRect(
            i * barWidth,
            itemHeight,
            barWidth,
            -bar.value * itemHeight
          );
        }
      })();
    },
    50,
    [dimensions, item.totalSize, zoom, bars]
  );

  return (
    <div>
      <canvas
        ref={canvasRef}
        style={{
          transform: `translateX(-${item.trimLeft * zoom}px)`,
          width: `${item.totalSize * zoom}px`,
          height: `${dimensions.itemHeight}px`,
        }}
      />
    </div>
  );
}
