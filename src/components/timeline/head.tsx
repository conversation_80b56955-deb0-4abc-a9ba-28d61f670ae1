import cn from "@meltdownjs/cn";
import { useTimelineContext } from "./use-timeline-store";

export function Head() {
  const headAt = useTimelineContext(s => s.headAt);
  const zoom = useTimelineContext(s => s.zoom);
  return (
    <div
      className={cn(
        "absolute top-0 h-[300px] w-[3px] bg-red-500",
        "border-l border-r border-white",
        "pointer-events-none"
      )}
      style={{
        left: `${headAt * zoom - 1}px`,
      }}
    />
  );
}
