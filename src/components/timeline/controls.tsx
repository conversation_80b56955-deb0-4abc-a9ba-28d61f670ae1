import cn from "@meltdownjs/cn";
import { useTimelineContext } from "./use-timeline-store";

export function Controls() {
  const zoom = useTimelineContext(s => s.zoom);
  const fitZoom = useTimelineContext(s => s.fitZoom);
  const setZoomWithFocalPoint = useTimelineContext(
    s => s.setZoomWithFocalPoint
  );
  const zoomBoundaries = useTimelineContext(s => s.zoomBoundaries);
  const isBusy = useTimelineContext(s => s.isBusy);
  const selected = useTimelineContext(s => s.selected);
  const splitItem = useTimelineContext(s => s.splitItem);
  const getItemStart = useTimelineContext(s => s.getItemStart);
  const headAt = useTimelineContext(s => s.headAt);
  const isHeadOnItem = useTimelineContext(s => s.isHeadOnItem);

  function handleSplitAtHead() {
    if (!selected) return;
    const start = getItemStart(selected.trackIndex, selected.itemIndex);

    splitItem(selected.id, headAt - start);
  }

  const isHeadOnSelected = selected ? isHeadOnItem(selected.id) : false;

  return (
    <div
      className={cn(
        "flex justify-center items-center gap-2",
        "border-t border-gray-200 py-2"
      )}
    >
      <input
        type="range"
        min={zoomBoundaries.min}
        max={zoomBoundaries.max}
        value={zoom}
        onChange={e => setZoomWithFocalPoint(Number(e.target.value))}
      />
      <button
        className={cn("bg-blue-500 text-white px-4 py-2 rounded-md")}
        onClick={() => fitZoom()}
      >
        Fit
      </button>
      <button
        className={cn(
          "bg-blue-500 text-white px-4 py-2 rounded-md",
          "disabled:opacity-50"
        )}
        disabled={!isHeadOnSelected}
        onClick={handleSplitAtHead}
      >
        Split at head
      </button>
      <div className={cn("opacity-0", isBusy && "opacity-100 animate-bounce")}>
        +
      </div>
    </div>
  );
}
