import { SCALE_HEIGHT } from "./scale";
import cn from "@meltdownjs/cn";
import { useTimelineContext } from "./use-timeline-store";

export function TrackLegends() {
  const tracks = useTimelineContext(s => s.tracks);
  const getTrackHeight = useTimelineContext(s => s.getTrackHeight);

  return (
    <div className="border-r border-t border-b border-gray-200 flex flex-col w-[200px]">
      <div className="w-full" style={{ height: SCALE_HEIGHT - 2 }} />
      {tracks.map((track, index) => (
        <div
          key={track.id}
          className={cn(
            "w-full flex flex-col justify-center px-2 border-t border-gray-200 last:border-b"
          )}
          style={{ height: getTrackHeight(index) }}
        >
          <span className="text-sm leading-3">{track.title}</span>
        </div>
      ))}
    </div>
  );
}
