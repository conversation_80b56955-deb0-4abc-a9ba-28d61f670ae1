import { useEffect, useMemo, useRef, useState } from "react";
import NextImage from "next/image";
import {
  calcItemHeight,
  getItemSize,
  TrackItem,
  useTimelineContext,
} from "./use-timeline-store";
import { ScaledImage } from "./video-frame";
import { getImage } from "./item-media-storage";
import { useDebounceEffect } from "./use-debounce-effect";
import { drawB64Image } from "./canvas-draw";

async function imgToB64(src: string, maxHeight: number): Promise<ScaledImage> {
  const canvas = document.createElement("canvas");
  const img = new Image();
  img.crossOrigin = "anonymous";
  img.src = src;

  await new Promise<void>((resolve, reject) => {
    img.onload = () => resolve();
    img.onerror = reject;
  });

  canvas.width = img.width;
  canvas.height = img.height;

  const scale = maxHeight / img.height;
  const width = img.width * scale;
  const height = img.height * scale;

  canvas.width = width;
  canvas.height = height;

  const ctx = canvas.getContext("2d");
  ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
  const b64 = canvas.toDataURL();

  return { b64, scale, width, height };
}

export function ImageItem({
  item,
  trackHeight,
}: {
  item: TrackItem;
  trackHeight: number;
}) {
  const zoom = useTimelineContext(s => s.zoom);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const dimensions = useMemo(() => {
    const itemHeight = calcItemHeight(trackHeight);
    const imageWidth = item.width ?? 0;
    const imageHeight = item.height ?? 0;
    const thumbnailHeight = itemHeight;
    const thumbnailWidth = (thumbnailHeight * imageWidth) / imageHeight;
    return {
      thumbnailWidth,
      thumbnailHeight,
      itemHeight,
      imageWidth,
      imageHeight,
    };
  }, [trackHeight, item.width, item.height]);

  const [image, setImage] = useState<ScaledImage | null>(null);

  useEffect(() => {
    (async () => {
      if (!item.imageUrl) return;
      setImage(
        await getImage(item.imageUrl, () => imgToB64(item.imageUrl!, 100))
      );
    })();
  }, [item.totalSize, item.id, item.imageUrl]);

  useDebounceEffect(
    () => {
      (async () => {
        const visibleCanvas = canvasRef.current;
        const ctx = visibleCanvas?.getContext("2d");

        const itemSize = getItemSize(item);
        const width = itemSize * zoom;
        const height = dimensions.itemHeight;

        const offscreen = document.createElement("canvas");
        offscreen.width = width;
        offscreen.height = height;
        const offCtx = offscreen.getContext("2d");

        if (!offCtx || !image || !ctx || !visibleCanvas) return;

        let dx = 0;
        while (dx < itemSize * zoom) {
          await drawB64Image(
            offCtx,
            image.b64,
            dx,
            0,
            dimensions.thumbnailWidth,
            dimensions.thumbnailHeight
          );
          dx += dimensions.thumbnailWidth;
        }

        visibleCanvas.width = width;
        visibleCanvas.height = height;
        ctx.drawImage(offscreen, 0, 0);

        visibleCanvas.style.width = `${itemSize * zoom}px`;
        visibleCanvas.style.height = `${dimensions.itemHeight}px`;
      })();
    },
    50,
    [image, zoom, dimensions, item.totalSize]
  );

  return (
    <div className="w-full overflow-hidden relative">
      <div className="absolute top-0 left-0 h-full">
        {item.imageUrl && (
          <NextImage
            src={item.imageUrl}
            alt="Timeline item"
            className="max-h-10"
            width={100}
            height={40}
            style={{ objectFit: "contain" }}
          />
        )}
      </div>
      <canvas ref={canvasRef} />
    </div>
  );
}
