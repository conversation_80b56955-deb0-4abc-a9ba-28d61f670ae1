import { Track } from "./track";
import { Scale } from "./scale";
import { Background } from "./background";
import { Head } from "./head";
import { useLeftDrag, useMoveDrag, useRightDrag } from "./use-item-drags";
import { TrackLegends } from "./track-legends";
import { Renderer } from "./track-item";
import { useTimelineContext } from "./use-timeline-store";
import { useTimelineHooks } from "./use-timeline-hooks";

export function Timeline({
  containerRef,
  renderer,
}: {
  containerRef: React.RefObject<HTMLDivElement | null>;
  renderer: Renderer;
}) {
  const tracks = useTimelineContext(s => s.tracks);
  const timelineSize = useTimelineContext(s => s.timelineSize);
  const zoom = useTimelineContext(s => s.zoom);
  const drag = useTimelineContext(s => s.dragState);

  useTimelineHooks(containerRef);

  useMoveDrag(drag);
  useRightDrag(drag);
  useLeftDrag(drag);

  return (
    <div className="flex w-full">
      <TrackLegends />
      <div
        className="overflow-x-scroll w-full relative overflow-y-hidden"
        ref={containerRef}
      >
        <Scale />
        <div className="relative" style={{ width: `${timelineSize * zoom}px` }}>
          <Background />
          <div className="flex flex-col">
            {tracks.map((_, index) => (
              <Track key={index} trackIndex={index} renderer={renderer} />
            ))}
            <div className="h-4 w-full" />
          </div>
        </div>
        <Head />
      </div>
    </div>
  );
}
