import { FC } from "react";
import DefaultAvatarSvg from "../../assets/default-avatar.svg";
import { SvgIcon } from "../common/svg-icon";

declare interface AvatarProps {
  size?: number;
  src?: string;
  className?: string;
  alt?: string;
}

export const Avatar: FC<AvatarProps> = ({ size = 40, className }) => {
  // TODO: Implement when we have image URLs
  return (
    <div>
      <SvgIcon
        src={DefaultAvatarSvg}
        alt="Default avatar"
        width={size}
        height={size}
        className={className}
      />
    </div>
  );
};
