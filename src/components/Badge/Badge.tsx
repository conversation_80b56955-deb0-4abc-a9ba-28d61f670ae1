import { ReactNode } from "react";
import { Badge as HeroBadge } from "@heroui/react";

interface BadgeProps {
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  content: string;
  size?: "sm" | "md" | "lg";
  children: ReactNode;
  className?: string;
}

export default function Badge({
  color = "default",
  content,
  size = "md",
  children,
  className = "",
}: BadgeProps) {
  return (
    <div className={`flex gap-3 items-center ${className}`}>
      <HeroBadge color={color} content={content} size={size}>
        {children}
      </HeroBadge>
    </div>
  );
}
