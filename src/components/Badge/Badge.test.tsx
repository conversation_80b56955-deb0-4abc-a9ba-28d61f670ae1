import { render, screen } from "@testing-library/react";
import Badge from "./Badge";
import Avatar from "../Avatar/Avatar";

describe("Badge", () => {
  it("renders the badge content and the avatar", () => {
    render(
      <Badge color="primary" content="5" size="sm">
        <Avatar src="https://i.pravatar.cc/150?u=a04258a2462d826712d" />
      </Badge>
    );

    expect(screen.getByText("5")).toBeInTheDocument();
    const avatarImg = screen.getByRole("img");
    expect(avatarImg).toHaveAttribute(
      "src",
      "https://i.pravatar.cc/150?u=a04258a2462d826712d"
    );
  });

  it("applies custom className to the wrapper", () => {
    render(
      <Badge color="success" content="99+" className="custom-badge">
        <Avatar src="https://i.pravatar.cc/150?u=example" />
      </Badge>
    );

    const wrapper = screen.getByText("99+").closest("div");
    expect(wrapper).toHaveClass("custom-badge");
  });
});
