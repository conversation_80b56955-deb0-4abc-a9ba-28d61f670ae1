import React from "react";
import { useForm, FieldValues } from "react-hook-form";
import Button from "@/components/Button/Button";
import { renderFields } from "./renderFields";
import { toolConfigs } from "./tool-configs";

interface ToolFormProps {
  toolKey: string;
  onSubmit: (data: FieldValues) => void;
  isLoading?: boolean;
}

export function ToolForm({
  toolKey,
  onSubmit,
  isLoading = false,
}: ToolFormProps) {
  const { control, handleSubmit, watch } = useForm<FieldValues>();
  const watchedValues = watch();

  // Get field configuration for this tool
  const fields = toolConfigs[toolKey];

  if (!fields) {
    return (
      <div className="text-gray-500 text-center py-8">
        No form configuration available for this tool.
      </div>
    );
  }

  // Transform form data to ensure correct types
  const transformFormData = (data: FieldValues) => {
    const transformed = { ...data };

    // Convert number fields from string to number
    fields.forEach((field) => {
      if (field.type === "number" && transformed[field.name] !== undefined) {
        const value = transformed[field.name];
        // Convert to number if it's a string
        if (typeof value === "string" && value !== "") {
          transformed[field.name] = Number(value);
        } else if (value === "") {
          // Handle empty strings for number fields with default values
          if (field.defaultValue !== undefined) {
            transformed[field.name] = field.defaultValue;
          }
        }
      }
    });

    return transformed;
  };

  const handleFormSubmit = (data: FieldValues) => {
    const transformedData = transformFormData(data);
    onSubmit(transformedData);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      {renderFields({ fields, control, watchedValues })}

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? "Generating..." : "Generate"}
      </Button>
    </form>
  );
}
