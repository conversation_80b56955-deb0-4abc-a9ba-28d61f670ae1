import React from "react";
import { Control, FieldValues } from "react-hook-form";
import {
  TextFieldControl,
  TextareaFieldControl,
  SelectFieldControl,
  SwitchFieldControl,
  FileFieldControl,
  FieldConfig,
} from "@/components/fields";

interface RenderFieldsProps {
  fields: FieldConfig[];
  control: Control<FieldValues>;
  watchedValues?: Record<string, unknown>;
}

/**
 * Utility function to render fields dynamically based on configuration
 * @param fields - Array of field configurations
 * @param control - React Hook Form control object
 * @param watchedValues - Optional watched values for conditional rendering
 */
export function renderFields({
  fields,
  control,
  watchedValues = {},
}: RenderFieldsProps) {
  return fields.map(field => {
    // Check render condition
    if (field.renderCondition && !field.renderCondition(watchedValues)) {
      return null;
    }

    // Base props that all fields share (excluding key)
    const baseProps = {
      name: field.name,
      control,
      label: field.label,
      placeholder: field.placeholder,
      description: field.description,
      required: field.required,
      disabled: field.disabled,
      className: field.className,
      containerClassName: field.containerClassName,
    };

    // Render based on field type
    switch (field.type) {
      case "text":
      case "email":
      case "password":
      case "number":
        return (
          <TextFieldControl key={field.name} {...baseProps} type={field.type} />
        );

      case "textarea":
        return (
          <TextareaFieldControl
            key={field.name}
            {...baseProps}
            rows={field.rows}
          />
        );

      case "select":
        return (
          <SelectFieldControl
            key={field.name}
            {...baseProps}
            options={field.options || []}
            allowEmpty={field.required ? false : true}
          />
        );

      case "switch":
      case "checkbox":
        return <SwitchFieldControl key={field.name} {...baseProps} />;

      case "file":
        return (
          <FileFieldControl
            key={field.name}
            {...baseProps}
            accept={field.accept}
            storagePath={field.storagePath}
            useUUID={field.useUUID}
          />
        );

      case "hidden":
        // Hidden fields don't need a visual component
        return null;

      case "custom":
        // Custom field with render function
        return field.render ? (
          <React.Fragment key={field.name}>
            {field.render({
              ...baseProps,
              control,
              watchedValues,
            })}
          </React.Fragment>
        ) : null;

      default:
        console.warn(`Unsupported field type: ${field.type}`);
        return null;
    }
  });
}

/**
 * Get grid span classes for layout
 */
export function getFieldGridClasses(
  gridSpan?: "full" | "half" | "third"
): string {
  switch (gridSpan) {
    case "full":
      return "col-span-12";
    case "third":
      return "col-span-12 md:col-span-4";
    case "half":
    default:
      return "col-span-12 md:col-span-6";
  }
}
