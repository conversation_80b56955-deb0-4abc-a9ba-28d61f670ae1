import React from "react";
import { Controller } from "react-hook-form";
import { FieldControlProps } from "@/components/fields/types";
import Textarea from "@/components/common/Textarea/Textarea";

type SimplePromptFieldControlProps = FieldControlProps;

export const SimplePromptFieldControl: React.FC<
  SimplePromptFieldControlProps
> = ({ name, control, rules, label, description, placeholder, required }) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue=""
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <SimplePromptField
          label={label}
          description={description}
          placeholder={placeholder}
          required={required}
          value={(value as string) || ""}
          onChange={onChange}
          error={error?.message}
        />
      )}
    />
  );
};

interface SimplePromptFieldProps {
  label?: string;
  description?: string;
  placeholder?: string;
  required?: boolean;
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

const SimplePromptField: React.FC<SimplePromptFieldProps> = ({
  label,
  description,
  placeholder,
  required,
  value,
  onChange,
  error,
}) => {
  return (
    <div className="mb-4">
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {description && (
        <p className="text-xs text-gray-500 mb-2">{description}</p>
      )}

      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />

      {error && <p className="text-xs text-red-500 mt-2">{error}</p>}
    </div>
  );
};
