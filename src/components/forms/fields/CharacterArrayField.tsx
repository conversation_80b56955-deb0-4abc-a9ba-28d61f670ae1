import React, { useState } from "react";
import { Controller } from "react-hook-form";
import { <PERSON><PERSON>, Card, CardBody } from "@heroui/react";
import { FieldControlProps } from "@/components/fields/types";
import { FileField } from "@/components/fields/FileField";
import { TextField } from "@/components/fields/TextField";
import { SelectField } from "@/components/fields/SelectField";
import { CHARACTER_SIZE_OPTIONS, CharacterSize } from "@/constants/generation";

interface Character {
  name: string;
  imageUrl: string;
  size: CharacterSize;
}

interface CharacterArrayFieldProps extends FieldControlProps {
  maxCharacters?: number;
}

export const CharacterArrayFieldControl: React.FC<CharacterArrayFieldProps> = ({
  name,
  control,
  rules,
  label,
  description,
  required,
  maxCharacters = 3,
}) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={[]}
      render={({ field: { onChange, value = [] }, fieldState: { error } }) => (
        <CharacterArrayField
          label={label}
          description={description}
          required={required}
          value={value}
          onChange={onChange}
          error={error?.message}
          maxCharacters={maxCharacters}
        />
      )}
    />
  );
};

interface CharacterArrayFieldInternalProps {
  label?: string;
  description?: string;
  required?: boolean;
  value: Character[];
  onChange: (value: Character[]) => void;
  error?: string;
  maxCharacters: number;
}

const CharacterArrayField: React.FC<CharacterArrayFieldInternalProps> = ({
  label,
  description,
  required,
  value,
  onChange,
  error,
  maxCharacters,
}) => {
  const [localCharacters, setLocalCharacters] = useState<Partial<Character>[]>(
    value.length > 0
      ? value
      : [{ name: "", imageUrl: "", size: CharacterSize.M }]
  );

  const handleCharacterChange = (
    index: number,
    field: keyof Character,
    fieldValue: string | CharacterSize
  ) => {
    const updatedCharacters = [...localCharacters];
    updatedCharacters[index] = {
      ...updatedCharacters[index],
      [field]: fieldValue,
    };
    setLocalCharacters(updatedCharacters);

    // Update form value only if all fields are filled
    const validCharacters = updatedCharacters.filter(
      char => char.name && char.imageUrl && char.size
    ) as Character[];
    onChange(validCharacters);
  };

  const addCharacter = () => {
    if (localCharacters.length < maxCharacters) {
      setLocalCharacters([
        ...localCharacters,
        { name: "", imageUrl: "", size: CharacterSize.M },
      ]);
    }
  };

  const removeCharacter = (index: number) => {
    const updatedCharacters = localCharacters.filter((_, i) => i !== index);
    setLocalCharacters(
      updatedCharacters.length > 0
        ? updatedCharacters
        : [{ name: "", imageUrl: "", size: CharacterSize.M }]
    );

    const validCharacters = updatedCharacters.filter(
      char => char.name && char.imageUrl && char.size
    ) as Character[];
    onChange(validCharacters);
  };

  return (
    <div className="mb-4">
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {description && (
        <p className="text-xs text-gray-500 mb-2">{description}</p>
      )}

      <div className="space-y-4">
        {localCharacters.map((character, index) => (
          <Card key={index} className="shadow-sm">
            <CardBody className="p-4">
              <div className="flex justify-between items-start mb-4">
                <h4 className="text-sm font-semibold text-gray-700">
                  Character {index + 1}
                </h4>
                {localCharacters.length > 1 && (
                  <Button
                    size="sm"
                    variant="light"
                    color="danger"
                    onClick={() => removeCharacter(index)}
                  >
                    Remove
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-12 gap-4">
                <div className="col-span-12 md:col-span-5">
                  <TextField
                    label="Character Name"
                    placeholder="Luna"
                    value={character.name || ""}
                    onChange={e =>
                      handleCharacterChange(index, "name", e.target.value)
                    }
                  />
                </div>

                <div className="col-span-12 md:col-span-2">
                  <SelectField
                    label="Size"
                    options={CHARACTER_SIZE_OPTIONS}
                    selectedKeys={new Set([character.size || CharacterSize.M])}
                    onSelectionChange={keys => {
                      const selectedSize = Array.from(keys)[0] as CharacterSize;
                      handleCharacterChange(index, "size", selectedSize);
                    }}
                    defaultSelectedKeys={[CharacterSize.M]}
                  />
                </div>

                <div className="col-span-12 md:col-span-5">
                  <FileField
                    label="Character Image"
                    accept="image/*"
                    storagePath="shot-characters"
                    useUUID={true}
                    value={character.imageUrl || ""}
                    onChange={result => {
                      handleCharacterChange(
                        index,
                        "imageUrl",
                        result?.url || ""
                      );
                    }}
                  />
                </div>
              </div>
            </CardBody>
          </Card>
        ))}

        {localCharacters.length < maxCharacters && (
          <Button variant="bordered" className="w-full" onClick={addCharacter}>
            Add Character (Max {maxCharacters})
          </Button>
        )}
      </div>

      {error && <p className="text-xs text-red-500 mt-2">{error}</p>}
    </div>
  );
};
