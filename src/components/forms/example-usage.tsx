/**
 * Example usage of the new field components
 */

import { useForm, FieldValues } from "react-hook-form";
import {
  TextFieldControl,
  SelectFieldControl,
  SwitchFieldControl,
  TextField,
  SelectField,
  FieldConfig,
} from "@/components/fields";
import { renderFields } from "./renderFields";

// Example 1: Using individual field components with Controller
export function ExampleFormIndividual() {
  const { control, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      role: "",
      isActive: false,
    },
  });

  const onSubmit = (data: FieldValues) => {
    console.log(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <TextFieldControl
        name="firstName"
        control={control}
        label="First Name"
        placeholder="Enter your first name"
        required
        rules={{ required: "First name is required" }}
      />

      <TextFieldControl
        name="lastName"
        control={control}
        label="Last Name"
        placeholder="Enter your last name"
        required
        rules={{ required: "Last name is required" }}
      />

      <TextFieldControl
        name="email"
        control={control}
        type="email"
        label="Email"
        placeholder="Enter your email"
        required
        rules={{
          required: "Email is required",
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: "Invalid email address",
          },
        }}
      />

      <SelectFieldControl
        name="role"
        control={control}
        label="Role"
        options={[
          { value: "ADMIN", label: "Administrator" },
          { value: "USER", label: "User" },
          { value: "GUEST", label: "Guest" },
        ]}
        required
        rules={{ required: "Please select a role" }}
      />

      <SwitchFieldControl
        name="isActive"
        control={control}
        label="Active Status"
        description="Enable or disable user account"
      />

      <button type="submit">Submit</button>
    </form>
  );
}

// Example 2: Using renderFields utility for dynamic forms
export function ExampleFormDynamic() {
  const { control, handleSubmit, watch } = useForm<FieldValues>();
  const watchedValues = watch();

  const fields: FieldConfig[] = [
    {
      name: "projectName",
      type: "text",
      label: "Project Name",
      placeholder: "Enter project name",
      required: true,
      gridSpan: "full",
    },
    {
      name: "projectType",
      type: "select",
      label: "Project Type",
      options: [
        { value: "WEB", label: "Web Application" },
        { value: "MOBILE", label: "Mobile App" },
        { value: "API", label: "API Service" },
      ],
      required: true,
      gridSpan: "half",
    },
    {
      name: "budget",
      type: "number",
      label: "Budget",
      placeholder: "0",
      gridSpan: "half",
    },
    {
      name: "hasDesign",
      type: "switch",
      label: "Has Design Files",
      gridSpan: "full",
    },
    {
      name: "designUrl",
      type: "text",
      label: "Design URL",
      placeholder: "https://...",
      gridSpan: "full",
      // Only show if hasDesign is true
      renderCondition: (values) => !!values.hasDesign,
    },
    {
      name: "description",
      type: "textarea",
      label: "Project Description",
      rows: 4,
      gridSpan: "full",
    },
  ];

  const onSubmit = (data: FieldValues) => {
    console.log(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="grid grid-cols-12 gap-4">
        {renderFields({ fields, control, watchedValues })}
      </div>
      <button type="submit" className="mt-4">
        Submit
      </button>
    </form>
  );
}

// Example 3: Using base components without form integration
export function ExampleStandalone() {
  return (
    <div className="space-y-4">
      <TextField
        label="Search"
        placeholder="Type to search..."
        onChange={(e) => console.log("Search:", e.target.value)}
      />

      <SelectField
        label="Filter by Status"
        options={[
          { value: "all", label: "All" },
          { value: "active", label: "Active" },
          { value: "inactive", label: "Inactive" },
        ]}
        onSelectionChange={(keys) => console.log("Selected:", Array.from(keys))}
      />
    </div>
  );
}
