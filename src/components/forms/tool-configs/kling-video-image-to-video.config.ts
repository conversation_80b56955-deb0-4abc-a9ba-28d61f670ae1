import { FieldConfig } from "@/components/fields";

export const klingVideoImageToVideoConfig: FieldConfig[] = [
  {
    name: "prompt",
    type: "textarea",
    label: "Video Description",
    placeholder: "Describe how you want the image to animate...",
    required: true,
    rows: 3,
    gridSpan: "full",
  },
  {
    name: "image_url",
    type: "file",
    label: "Image to Animate",
    accept: "image/*",
    required: true,
    gridSpan: "full",
    storagePath: "video-sources",
    useUUID: true,
  },
  {
    name: "duration",
    type: "select",
    label: "Duration",
    required: false,
    defaultValue: "5",
    options: [
      { value: "5", label: "5 seconds" },
      { value: "10", label: "10 seconds" },
    ],
  },
];
