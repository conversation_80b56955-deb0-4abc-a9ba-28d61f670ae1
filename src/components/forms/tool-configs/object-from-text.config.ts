import { FieldConfig } from "@/components/fields";
import {
  STYLE_OPTIONS,
  DEFAULT_STYLE,
  DEFAULT_SEED,
  DEFAULT_NUMBER_OF_IMAGES,
} from "@/constants/generation";

export const objectFromTextConfig: FieldConfig[] = [
  {
    name: "prompt",
    type: "textarea",
    label: "Object Description",
    placeholder: "Describe the object or item you want to generate...",
    required: true,
    rows: 4,
    gridSpan: "full",
  },
  {
    name: "numberOfImages",
    type: "number",
    label: "Number of Images",
    placeholder: "1-4",
    defaultValue: DEFAULT_NUMBER_OF_IMAGES,
    gridSpan: "half",
  },
  {
    name: "style",
    type: "select",
    label: "Style",
    options: STYLE_OPTIONS,
    defaultValue: DEFAULT_STYLE,
    gridSpan: "half",
  },
  {
    name: "seed",
    type: "number",
    label: "Seed",
    placeholder: "-1 for random",
    defaultValue: DEFAULT_SEED,
    gridSpan: "half",
  },
];
