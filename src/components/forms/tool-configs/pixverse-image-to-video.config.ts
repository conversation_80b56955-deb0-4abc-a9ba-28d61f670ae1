import { FieldConfig } from "@/components/fields";

export const pixverseImageToVideoConfig: FieldConfig[] = [
  {
    name: "prompt",
    type: "textarea",
    label: "Motion Description",
    placeholder: "Describe how you want the image to animate...",
    required: true,
    rows: 3,
    gridSpan: "full",
  },
  {
    name: "image_url",
    type: "file",
    label: "Image to Animate",
    accept: "image/*",
    required: true,
    gridSpan: "full",
    storagePath: "video-sources",
    useUUID: true,
  },
];
