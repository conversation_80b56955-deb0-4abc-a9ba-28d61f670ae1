import { FieldConfig } from "@/components/fields";
import { fluxDevConfig } from "./flux-dev.config";
import { humanAvatarFromImageConfig } from "./human-avatar-from-image.config";
import { humanAvatarFromTextAndImageConfig } from "./human-avatar-from-text-and-image.config";
import { humanAvatarFromTextConfig } from "./human-avatar-from-text.config";
import { nonHumanCharacterFromTextConfig } from "./non-human-character-from-text.config";
import { objectFromTextConfig } from "./object-from-text.config";
import { shotGeneratorConfig } from "./shot-generator.config";
import { seedanceTextToVideoConfig } from "./seedance-text-to-video.config";
import { seedanceImageToVideoConfig } from "./seedance-image-to-video.config";
import { pixverseImageToVideoConfig } from "./pixverse-image-to-video.config";
import { runwayGen4ImageConfig } from "./runway-gen4-image.config";
import { klingVideoImageToVideoConfig } from "./kling-video-image-to-video.config";
import { fluxKreaGenerateImageConfig } from "./flux-krea-generate-image.config";
import { veo3TextToVideoConfig } from "./veo3-text-to-video.config";
import { topazUpscaleVideoConfig } from "./topaz-upscale-video.config";

// Simple mapping of tool keys to field configurations
export const toolConfigs: Record<string, FieldConfig[]> = {
  // Image generation tools
  "flux-generate-image": fluxDevConfig,
  "flux-krea-generate-image": fluxKreaGenerateImageConfig,
  "runway-gen4-image": runwayGen4ImageConfig,

  // Avatar tools
  "human-avatar-from-image": humanAvatarFromImageConfig,
  "human-avatar-from-text-and-image": humanAvatarFromTextAndImageConfig,
  "human-avatar-from-text": humanAvatarFromTextConfig,
  "non-human-character-from-text": nonHumanCharacterFromTextConfig,

  // Object tools
  "object-from-text": objectFromTextConfig,

  // Shot generation
  "shot-generator": shotGeneratorConfig,

  // Video tools
  "seedance-text-to-video": seedanceTextToVideoConfig,
  "seedance-image-to-video": seedanceImageToVideoConfig,
  "pixverse-image-to-video": pixverseImageToVideoConfig,
  "kling-video-image-to-video": klingVideoImageToVideoConfig,
  "veo3-text-to-video": veo3TextToVideoConfig,
  "topaz-upscale-video": topazUpscaleVideoConfig,
};
