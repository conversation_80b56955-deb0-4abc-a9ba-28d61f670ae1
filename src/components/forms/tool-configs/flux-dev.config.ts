import { FieldConfig } from "@/components/fields";
import { FLUX_ASPECT_RATIO_OPTIONS, FluxAspectRatio } from "@/constants/generation";

export const fluxDevConfig: FieldConfig[] = [
  {
    name: "prompt",
    type: "textarea",
    label: "Image Description",
    placeholder: "Describe the image you want to generate...",
    required: true,
    rows: 4,
    gridSpan: "full",
  },
  {
    name: "image_size",
    type: "select",
    label: "Aspect Ratio",
    options: FLUX_ASPECT_RATIO_OPTIONS,
    defaultValue: FluxAspectRatio.LANDSCAPE_16_9,
    gridSpan: "half",
  },
  {
    name: "seed",
    type: "number",
    label: "Seed",
    placeholder: "Leave empty for random",
    gridSpan: "half",
  },
];