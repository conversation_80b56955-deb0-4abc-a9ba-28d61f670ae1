import { FieldConfig } from "@/components/fields";

export const topazUpscaleVideoConfig: FieldConfig[] = [
  {
    name: "video_url",
    type: "file",
    label: "Video to Upscale",
    accept: "video/*",
    required: true,
    gridSpan: "full",
    storagePath: "video-sources",
    useUUID: true,
  },
  {
    name: "upscale_factor",
    type: "number",
    label: "Upscale Factor",
    placeholder: "2",
    defaultValue: 2,
    description: "Factor to upscale (1-8, e.g. 2.0 doubles width and height)",
    required: false,
    gridSpan: "half",
  },
  {
    name: "target_fps",
    type: "number",
    label: "Target FPS (optional)",
    placeholder: "Leave empty to keep original FPS",
    description: "If set, frame interpolation will be enabled (1-120)",
    required: false,
    gridSpan: "half",
  },
];
