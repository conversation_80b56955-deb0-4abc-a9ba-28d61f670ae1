import { FieldConfig } from "@/components/fields";
import React from "react";
import { CharacterArrayFieldControl } from "../fields/CharacterArrayField";
import { SimplePromptFieldControl } from "../fields/SimplePromptField";
import {
  STYLE_OPTIONS,
  ORIENTATION_OPTIONS,
  DEFAULT_STYLE,
  DEFAULT_ORIENTATION,
  DEFAULT_SEED,
} from "@/constants/generation";

export const shotGeneratorConfig: FieldConfig[] = [
  {
    name: "characters",
    type: "custom",
    label: "Characters",
    description: "Add up to 3 characters with their reference images",
    required: true,
    gridSpan: "full",
    render: (props: Record<string, unknown>) =>
      React.createElement(CharacterArrayFieldControl, {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ...(props as any),
        maxCharacters: 3,
      }),
  },
  {
    name: "prompt",
    type: "custom",
    label: "Scene Description",
    placeholder: "Describe the scene with your characters...",
    required: true,
    gridSpan: "full",
    render: (props: Record<string, unknown>) => {
      return React.createElement(SimplePromptFieldControl, {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ...(props as any),
        placeholder: "Describe the scene with your characters...",
      });
    },
  },
  {
    name: "style",
    type: "select",
    label: "Visual Style",
    options: STYLE_OPTIONS,
    defaultValue: DEFAULT_STYLE,
    gridSpan: "half",
  },
  {
    name: "imageOrientation",
    type: "select",
    label: "Orientation",
    options: ORIENTATION_OPTIONS,
    defaultValue: DEFAULT_ORIENTATION,
    gridSpan: "half",
  },
  {
    name: "enhancePrompt",
    type: "switch",
    label: "Enhance Prompt",
    defaultValue: false,
    gridSpan: "half",
  },
  {
    name: "imageEnhancer",
    type: "switch",
    label: "Enhance Image Quality",
    defaultValue: false,
    gridSpan: "half",
  },
  {
    name: "seed",
    type: "number",
    label: "Seed",
    placeholder: "-1 for random",
    defaultValue: DEFAULT_SEED,
    gridSpan: "half",
  },
];
