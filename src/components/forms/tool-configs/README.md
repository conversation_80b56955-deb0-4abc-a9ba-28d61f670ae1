# Tool Form Configurations

Simple UI configurations for AI generation tools.

## Structure

```
tool-configs/
├── index.ts              # Main registry mapping tool keys to configs
├── flux-dev.config.ts    # Flux image generation form config
└── README.md            # This file
```

## How It Works

1. **Backend** provides Zod schema as JSON Schema (already validates on server)
2. **Frontend** provides UI configuration for better UX
3. **No conversion needed** - Backend handles validation, frontend handles presentation

## Adding a New Tool

1. Create a new config file:

```typescript
// comfy-avatar.config.ts
import { FieldConfig } from "@/components/fields";

export const comfyAvatarConfig: FieldConfig[] = [
  {
    name: "characterId",
    type: "custom",
    label: "Select Character",
    render: (props) => <CharacterSelect {...props} />,
  },
  {
    name: "prompt",
    type: "text",
    label: "Scene Description",
  },
];
```

2. Add to index.ts:

```typescript
export const toolConfigs: Record<string, FieldConfig[]> = {
  "flux-generate-image": fluxDevConfig,
  "comfy-avatar-generator": comfyAvatarConfig, // Add here
};
```

## Field Types Available

- `text`, `email`, `password`, `number` - Basic inputs
- `textarea` - Multi-line text
- `select` - Dropdown with options
- `switch` - Toggle/boolean
- `file` - File upload
- `custom` - Any React component via render function

## Example Usage

```tsx
import { ToolForm } from "@/components/forms";

<ToolForm
  toolKey="flux-generate-image"
  onSubmit={data => console.log(data)}
  isLoading={false}
/>;
```

That's it! Keep it simple.
