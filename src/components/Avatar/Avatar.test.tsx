import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import Avatar from "./Avatar";

describe("AvatarComponent", () => {
  const mockSrc = "https://i.pravatar.cc/150?u=test-user";

  it("renders the Avatar with a given src", () => {
    render(<Avatar src={mockSrc} />);
    const avatarImg = screen.getByRole("img");
    expect(avatarImg).toBeInTheDocument();
    expect(avatarImg).toHaveAttribute("src", mockSrc);
  });

  it("applies the isBordered prop by default (true)", () => {
    render(<Avatar src={mockSrc} />);
    const avatarImg = screen.getByRole("img");
    expect(avatarImg).toHaveAttribute("src", mockSrc);
  });

  it("respects the isBordered prop when false", () => {
    render(<Avatar src={mockSrc} isBordered={false} />);
    const avatarImg = screen.getByRole("img");
    expect(avatarImg).toHaveAttribute("src", mockSrc);
  });

  it("renders with custom color prop", () => {
    render(<Avatar src={mockSrc} color="success" />);
    const avatarImg = screen.getByRole("img");
    expect(avatarImg).toHaveAttribute("src", mockSrc);
  });
});
