import { Avatar } from "@heroui/react";

export interface AvatarProps {
  isBordered?: boolean;
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  src: string;
}

export default function App({
  isBordered = true,
  color = "default",
  src,
}: AvatarProps) {
  return (
    <div className="flex gap-4 items-center">
      <Avatar isBordered={isBordered} color={color} src={src} />
    </div>
  );
}
