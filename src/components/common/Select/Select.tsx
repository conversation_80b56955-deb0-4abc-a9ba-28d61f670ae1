import React from "react";
import { Controller, Control } from "react-hook-form";
import {
  Select as HeroSelect,
  SelectProps as HeroSelectProps,
} from "@heroui/react";

export interface SelectProps extends HeroSelectProps {
  name?: string;
  control?: Control<any>;
}

const Select: React.FC<SelectProps> = ({
  name,
  control,
  label,
  children,
  ...inputProps
}) => {
  if (name && control) {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <div className="mb-4">
            {label && (
              <label
                htmlFor={name}
                className="text-xs text-indigo-500 font-semibold mb-2 block"
              >
                {label}
              </label>
            )}
            <HeroSelect
              {...inputProps}
              label={label}
              value={value ?? ""}
              onChange={onChange}
              onBlur={onBlur}
              classNames={{
                base: [
                  "text-xs w-full border-2 border-[#B2B2EF] rounded",
                  "focus:ring-2 focus:ring-indigo-300 shadow-sm",
                ],
                innerWrapper: "bg-transparent",
              }}
            >
              {children}
            </HeroSelect>
          </div>
        )}
      />
    );
  }

  return <HeroSelect {...(inputProps as SelectProps)}>{children}</HeroSelect>;
};

export default Select;
