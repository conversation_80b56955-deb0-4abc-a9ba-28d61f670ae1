import { ReactNode, FC } from "react";

export type Variant = "primary" | "rounded" | "outline" | "ghost";

export interface ButtonProps {
  title?: string;
  icon?: ReactNode;
  width?: number;
  height?: number;
  onClick?: () => void;
  disabled?: boolean;
  variant?: Variant;
  className?: string;
}

export const Button: FC<ButtonProps> = ({
  title,
  icon,
  width,
  height,
  onClick,
  disabled = false,
  variant = "primary",
  className,
}) => {
  const baseClasses = [
    "h-8 inline-flex items-center justify-center px-4 gap-2",
    "text-xs font-semibold",
    "focus:outline-none focus:ring-0 focus:ring-offset-0",
    "transition-colors duration-150 cursor-pointer",
  ];

  const variantClasses: Record<Variant, string[]> = {
    primary: [
      "rounded-lg bg-indigo-500 text-white",
      "hover:bg-purple-700 focus:ring-purple-500",
      "disabled:bg-[#B2B2EF] disabled:cursor-not-allowed",
    ],

    rounded: [
      "rounded-full bg-[#EAEAFB] text-indigo-500 border-1 border-[#EAEAFB]",
      "hover:bg-purple-50 focus:ring-purple-500",
      "disabled:bg-gray-200 disabled:border-[#F4F4F5] disabled:text-indigo-300 disabled:cursor-not-allowed",
    ],

    outline: [
      "rounded-lg bg-white text-indigo-500 border-1 border-indigo-500",
      "hover:bg-purple-50 hover:shadow-[0px_1px_2px_0px_#7828C866] focus:ring-purple-500",
      "disabled:bg-gray-200 disabled:border-[#F4F4F5] disabled:text-indigo-300 disabled:cursor-not-allowed",
    ],

    ghost: [
      "rounded-lg bg-transparent text-indigo-500",
      "hover:text-black focus:ring-purple-500",
      "disabled:text-gray-300 disabled:cursor-not-allowed",
    ],
  };

  baseClasses.push(width ? "w-" + width : "w-full");
  baseClasses.push(height ? "h-" + height : "h-8");

  return (
    <button
      type="button"
      className={`${[...baseClasses, ...variantClasses[variant]].join(" ")} ${className}`}
      onClick={onClick}
      disabled={disabled}
    >
      {icon && <span className="flex-shrink-0">{icon}</span>}
      {title && <span>{title}</span>}
    </button>
  );
};
