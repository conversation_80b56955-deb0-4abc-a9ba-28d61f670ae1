import { FC } from "react";

interface ToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
}

export const Toggle: FC<ToggleProps> = ({ checked, onChange, label }) => (
  <div className="flex items-center justify-between mb-4">
    <span className="text-xs font-semibold text-indigo-500 cursor-pointer">
      {label}
    </span>
    <label className="relative inline-block h-6 w-10">
      <input
        type="checkbox"
        className="opacity-0 w-0 h-0"
        checked={checked}
        onChange={e => onChange(e.target.checked)}
      />
      <span
        className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full transition-colors ${
          checked ? "bg-indigo-500" : "bg-[#D8D8F7]"
        }`}
      />
      <span
        className={`absolute left-1 top-1 block w-4 h-4 bg-white rounded-full transition-transform ${
          checked ? "translate-x-4" : ""
        }`}
      />
    </label>
  </div>
);
