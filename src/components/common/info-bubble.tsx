import { FC } from "react";

interface InfoBubbleProps {
  title: string;
  details: string;
}

export const InfoBubble: FC<InfoBubbleProps> = ({ title, details }) => (
  <div className="relative z-20 flex flex-col space-y-1 p-4 text-base bg-gradient-to-r from-[#F3ECF9] to-[#FFF0FE] shadow-[0px_4px_6px_-1px_#7828C866] text-indigo-500 rounded-tl-xl rounded-br-xl border-[1.36px] border-white">
    <p className="font-bold text-left">{title}</p>
    <p className="italic">{details}</p>
  </div>
);
