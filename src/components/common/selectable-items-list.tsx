import { FC } from "react";
import { SelectableItem } from "@/components/common/selectable-item";

export interface Item {
  id: string;
  imageUrl?: string;
  text?: string;
  caption?: string;
}

export interface SelectableItemsListProps {
  items?: Item[];
  selected: string | null;
  setSelected: (selected: string | null) => void;
}

export const SelectableItemsList: FC<SelectableItemsListProps> = ({
  items = [],
  selected,
  setSelected,
}) => {
  return (
    <div className="h-[466px] overflow-y-auto space-y-4 px-4 py-6">
      {items.map(({ id, imageUrl, text, caption }) => (
        <SelectableItem
          key={id}
          id={id}
          url={imageUrl}
          text={text}
          caption={caption}
          selected={selected === id}
          onSelect={() => setSelected(id)}
        />
      ))}
    </div>
  );
};
