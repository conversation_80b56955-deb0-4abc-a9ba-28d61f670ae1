import React from "react";
import { Controller, Control } from "react-hook-form";
import {
  Input as HeroInput,
  InputProps as HeroInputProps,
} from "@heroui/react";

export interface InputProps extends HeroInputProps {
  name?: string;
  control?: Control<any>;
}

const Input: React.FC<InputProps> = ({
  name,
  control,
  label,
  ...inputProps
}) => {
  if (name && control) {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <div className="mb-4">
            {label && (
              <label
                htmlFor={name}
                className="text-xs text-indigo-500 font-semibold mb-2 block"
              >
                {label}
              </label>
            )}
            <HeroInput
              {...inputProps}
              value={value ?? ""}
              onChange={onChange}
              onBlur={onBlur}
              classNames={{
                base: [
                  "text-xs w-full border-2 border-[#B2B2EF] rounded",
                  "focus:ring-2 focus:ring-indigo-300 shadow-sm",
                ],
                inputWrapper: "bg-transparent",
              }}
            />
          </div>
        )}
      />
    );
  }

  return <HeroInput {...(inputProps as HeroInputProps)} />;
};

export default Input;
