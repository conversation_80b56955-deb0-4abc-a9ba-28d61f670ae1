import React, { FC, ReactNode } from "react";
import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@heroui/react";
import { SvgIcon } from "@/components/common/svg-icon";
import CloseIconSvg from "@/assets/close.svg";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: ReactNode;
  width?: string | number;
  height?: string | number;
  children: ReactNode;
  footer?: ReactNode;
}

const Modal: FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  width,
  height,
  children,
  footer,
}) => {
  const widthValue = typeof width === "number" ? `${width}px` : width;
  const heightValue = typeof height === "number" ? `${height}px` : height;
  const baseClasses = [
    widthValue ? `w-[${widthValue}]` : "",
    heightValue ? `h-[${heightValue}]` : "",
  ]
    .filter(<PERSON><PERSON><PERSON>)
    .join(" ");

  return (
    <HeroModal
      isOpen={isOpen}
      onOpenChange={open => {
        if (!open) onClose();
      }}
      classNames={{
        ...(baseClasses ? { base: `${baseClasses} max-w-none` } : {}),
        body: "p-0",
      }}
      radius="sm"
      closeButton={
        <button onClick={onClose} className="text-gray-600 hover:text-gray-900">
          <SvgIcon src={CloseIconSvg} alt="Close" width={16} height={16} />
        </button>
      }
    >
      <ModalContent
        style={{
          width: width ?? undefined,
          height: height ?? undefined,
        }}
      >
        {title && (
          <ModalHeader
            className="h-12 flex items-center text-lg font-semibold text-indigo-500 px-3 bg-gradient-to-r from-[#F3ECF9] to-[#EAEAFB]
    shadow-md"
          >
            {title}
          </ModalHeader>
        )}
        <ModalBody>{children}</ModalBody>
        {footer && <ModalFooter>{footer}</ModalFooter>}
      </ModalContent>
    </HeroModal>
  );
};

export default Modal;
