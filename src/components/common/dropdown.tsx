import ArrowRightIconSvg from "../../assets/arrow-right.svg";
import { SvgIcon } from "./svg-icon";
import { FC, ReactNode, useState } from "react";

export interface DropdownOption {
  id: string;
  label: string;
  description?: string;
  icon?: ReactNode;
}

export interface DropdownProps {
  label?: string;
  options: DropdownOption[];
  value?: string;
  placeholder: string;
  onChange: (id: string) => void;
}

export const Dropdown: FC<DropdownProps> = ({
  label,
  options,
  value,
  placeholder,
  onChange,
}) => {
  const [open, setOpen] = useState(false);
  const selected = options.find(o => o.id === value);

  return (
    <div className="relative w-full mb-6">
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
        </label>
      )}
      <button
        type="button"
        onClick={() => setOpen(prev => !prev)}
        className="w-full bg-white border border-[#B2B2EF] rounded p-2 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-indigo-500"
      >
        <div className="flex items-center space-x-2">
          {selected ? (
            <div className="flex gap-2">
              {selected.icon && <span>{selected.icon}</span>}
              <div className="flex flex-col text-sm text-indigo-500 text-left">
                <span className="font-semibold">{selected.label}</span>
                <p>{selected.description}</p>
              </div>
            </div>
          ) : (
            <span className="text-[#11181C] text-xs">{placeholder}</span>
          )}
        </div>
        {selected && (
          <SvgIcon
            src={ArrowRightIconSvg}
            alt="Selected"
            width={16}
            height={16}
          />
        )}
      </button>
      {open && (
        <ul className="absolute mt-1 w-full bg-white border border-indigo-300 rounded-lg shadow-lg z-9999">
          {options.map(opt => (
            <li
              key={opt.id}
              onClick={() => {
                onChange(opt.id);
                setOpen(false);
              }}
              className={`flex items-center justify-between p-2 hover:bg-indigo-500 hover:text-white ${
                value === opt.id
                  ? "bg-indigo-500 text-white"
                  : "text-indigo-500"
              }`}
            >
              <div className="flex items-center space-x-2">
                {opt.icon && <span>{opt.icon}</span>}
                <div className="text-left">
                  <div className="font-medium text-sm">{opt.label}</div>
                  {opt.description && (
                    <div className="text-xs">{opt.description}</div>
                  )}
                </div>
              </div>
              {value === opt.id && (
                <SvgIcon
                  src={ArrowRightIconSvg}
                  alt="Selected"
                  width={16}
                  height={16}
                />
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
