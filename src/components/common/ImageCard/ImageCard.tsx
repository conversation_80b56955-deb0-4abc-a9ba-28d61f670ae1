import { SvgIcon } from "@/components/common/svg-icon";
import Logo from "@/assets/logo.svg";
import { FC } from "react";
import Image from "next/image";

export interface ImageCardProps {
  imageUrl?: string;
}

export const ImageCard: FC<ImageCardProps> = ({ imageUrl }) => {
  return (
    <div className="shrink-0 flex justify-center items-center rounded-lg w-[358px] h-[440px] bg-gradient-to-b from-[rgba(62,62,216,0.1)] to-[rgba(127,43,188,0.1)]">
      {imageUrl ? (
        <Image
          src={imageUrl}
          alt="Card image"
          width={358}
          height={440}
          className="object-contain"
        />
      ) : (
        <SvgIcon
          src={Logo}
          alt="logo"
          className="opacity-25"
          width={84}
          height={46}
        />
      )}
    </div>
  );
};
