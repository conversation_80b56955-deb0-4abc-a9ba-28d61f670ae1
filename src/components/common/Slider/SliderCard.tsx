import React from "react";

interface SliderCardProps {
  children: React.ReactNode;
  className?: string;
}

const SliderCard: React.FC<SliderCardProps> = ({
  children,
  className = "",
}) => {
  return (
    <div
      className={`
      relative 
      bg-gradient-to-br from-purple-300 via-purple-400 to-purple-500
      rounded-2xl 
      p-8 
      shadow-lg 
      flex 
      flex-col 
      justify-center 
      items-center
      text-white
      overflow-hidden
      w-[415px]
      h-[415px]
      ${className}
    `}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-white/10 rounded-2xl" />

      <div className="relative z-10 w-full h-full flex flex-col justify-center items-center">
        {children}
      </div>
    </div>
  );
};

export default SliderCard;
