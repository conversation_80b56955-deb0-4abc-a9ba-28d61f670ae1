"use client";

import React from "react";

interface SliderProps {
  items: React.ReactNode[];
  className?: string;
  itemHeight?: string;
}

const Slider: React.FC<SliderProps> = ({
  items,
  className = "",
  itemHeight = "415px",
}) => {
  return (
    <div className={`grid grid-cols-2 gap-4 h-full pl-2 ${className}`}>
      {items.map((item, index) => (
        <div
          key={index}
          className="flex items-center justify-center"
          style={{ height: itemHeight }}
        >
          {item}
        </div>
      ))}
    </div>
  );
};

export default Slider;
