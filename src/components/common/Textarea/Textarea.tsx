import React from "react";
import { Controller, Control } from "react-hook-form";
import {
  Textarea as HeroTextarea,
  TextAreaProps as HeroTextAreaProps,
} from "@heroui/react";

export interface TextareaProps extends HeroTextAreaProps {
  name?: string;
  control?: Control<any>;
}

const Textarea: React.FC<TextareaProps> = ({
  name,
  control,
  label,
  ...textareaProps
}) => {
  if (name && control) {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, onBlur, value } }) => (
          <div className="mb-4">
            {label && (
              <label
                htmlFor={name}
                className="text-xs text-indigo-500 font-semibold mb-2 block"
              >
                {label}
              </label>
            )}
            <HeroTextarea
              {...textareaProps}
              value={value ?? ""}
              onChange={onChange}
              onBlur={onBlur}
              classNames={{
                base: [
                  "text-xs w-full border-2 border-[#B2B2EF] rounded",
                  "focus:ring-2 focus:ring-indigo-300 shadow-sm",
                ],
                inputWrapper: "bg-transparent",
              }}
            />
          </div>
        )}
      />
    );
  }

  return <HeroTextarea {...(textareaProps as HeroTextAreaProps)} />;
};

export default Textarea;
