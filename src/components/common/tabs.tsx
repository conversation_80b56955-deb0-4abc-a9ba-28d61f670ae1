import { FC } from "react";

export interface TabItem {
  id: string;
  label: string;
  count?: number;
}

interface TabsProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (id: string) => void;
}

export const Tabs: FC<TabsProps> = ({ tabs, activeTab, onTabChange }) => (
  <div className="p-3">
    <div className="flex bg-[#D8D8F7] rounded-md overflow-hidden p-1">
      {tabs.map(tab => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`text-indigo-500 h-6 flex-1 flex items-center justify-center text-xs font-semibold rounded-lg ${
            activeTab === tab.id ? "bg-white" : ""
          }`}
        >
          <div className="relative">
            {tab.label}
            {tab.count && tab.count > 0 ? (
              <span className="h-4 w-4 flex justify-center items-center absolute top-0 -right-5 bg-indigo-500 text-white text-xs rounded-full">
                {tab.count}
              </span>
            ) : (
              ""
            )}
          </div>
        </button>
      ))}
    </div>
  </div>
);
