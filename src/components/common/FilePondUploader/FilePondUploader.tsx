"use client";

import React, { useState, useRef, FC } from "react";
import { FilePond, registerPlugin } from "react-filepond";
import type { FilePondProps } from "react-filepond";
// import type { ActualFileObject } from "filepond";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginFileValidateSize from "filepond-plugin-file-validate-size";
import { uploadFile, deleteFile, UploadOptions } from "@/lib/firebase-storage";

// Import CSS
import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css";

// Register plugins
registerPlugin(
  FilePondPluginImagePreview,
  FilePondPluginFileValidateType,
  FilePondPluginFileValidateSize
);

export interface FirebaseFile {
  url: string;
  path: string;
  fileName: string;
}

interface FilePondUploaderProps extends Partial<FilePondProps> {
  // Firebase specific props
  storagePath?: string;
  useUUID?: boolean;
  metadata?: Record<string, string>;

  // Component props
  onUploadComplete?: (file: FirebaseFile) => void;
  onUploadError?: (error: Error) => void;
  onFileRemoved?: (file: FirebaseFile) => void;

  // FilePond overrides
  acceptedFileTypes?: string[];
  maxFileSize?: string;
  allowMultiple?: boolean;
  maxFiles?: number;
  instantUpload?: boolean;
}

const FilePondUploader: FC<FilePondUploaderProps> = ({
  storagePath = "uploads",
  useUUID = true,
  metadata: userMetadata,
  onUploadComplete,
  onUploadError,
  onFileRemoved,
  acceptedFileTypes = ["image/*"],
  maxFileSize = "10MB",
  allowMultiple = false,
  maxFiles = 1,
  instantUpload = true,
  ...filePondProps
}) => {
  const [files, setFiles] = useState<any[]>([]);
  const uploadedFilesRef = useRef<Map<string, FirebaseFile>>(new Map());

  const handleProcessFile = (
    fieldName: string,
    file: any, // FilePond passes its own file type
    metadata: any,
    load: (response: any) => void,
    error: (errorText: string) => void,
    progress: (computable: boolean, loaded: number, total: number) => void,
    abort: (callback: () => void) => void
  ) => {
    let aborted = false;

    const abortHandler = () => {
      aborted = true;
      error("Upload cancelled");
    };

    // Process the upload asynchronously
    (async () => {
      try {
        const uploadOptions: UploadOptions = {
          path: storagePath,
          useUUID,
          metadata: userMetadata || {},
        };

        const result = await uploadFile(
          file,
          uploadOptions,
          progressPercent => {
            if (!aborted) {
              // Convert percentage to bytes for FilePond
              const loaded = (progressPercent / 100) * file.size;
              progress(true, loaded, file.size);
            }
          }
        );

        if (!aborted) {
          // Store the uploaded file info
          uploadedFilesRef.current.set(file.name, result);

          // Notify parent component
          onUploadComplete?.(result);

          // Tell FilePond the upload is complete
          load(result);
        }
      } catch (err) {
        if (!aborted) {
          const errorMessage =
            err instanceof Error ? err.message : "Upload failed";
          error(errorMessage);
          onUploadError?.(err as Error);
        }
      }
    })();

    // Set the abort callback
    abort(abortHandler);
  };

  const handleRemoveFile = async (
    source: any,
    load: () => void,
    error: (errorText: string) => void
  ) => {
    try {
      // Get the uploaded file info
      const uploadedFile = uploadedFilesRef.current.get(source.fileName);

      if (uploadedFile) {
        // Delete from Firebase Storage
        await deleteFile(uploadedFile.path);

        // Remove from our reference map
        uploadedFilesRef.current.delete(source.fileName);

        // Notify parent component
        onFileRemoved?.(uploadedFile);
      }

      // Tell FilePond the removal is complete
      load();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Delete failed";
      error(errorMessage);
    }
  };

  return (
    <FilePond
      files={files}
      onupdatefiles={setFiles}
      allowMultiple={allowMultiple}
      maxFiles={maxFiles}
      instantUpload={instantUpload}
      acceptedFileTypes={acceptedFileTypes}
      maxFileSize={maxFileSize}
      labelIdle='Drag & Drop your files or <span class="filepond--label-action">Browse</span>'
      server={{
        process: handleProcessFile,
        revert: handleRemoveFile,
      }}
      {...filePondProps}
    />
  );
};

export default FilePondUploader;
