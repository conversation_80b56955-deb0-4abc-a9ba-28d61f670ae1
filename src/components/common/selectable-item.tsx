import { FC } from "react";
import RadioOnIconSvg from "../../assets/radio-on.svg";
import RadioOffIconSvg from "../../assets/radio-off.svg";
import Image from "next/image";
import { SvgIcon } from "./svg-icon";

export interface SelectableItemProps {
  id: string;
  url?: string;
  text?: string;
  caption?: string;
  selected: boolean;
  onSelect: (id: string) => void;
}

export const SelectableItem: FC<SelectableItemProps> = ({
  id,
  url,
  text,
  caption,
  selected,
  onSelect,
}) => (
  <div
    className="relative flex flex-col space-y-1 cursor-pointer"
    onClick={() => onSelect(id)}
  >
    {url ? (
      <Image
        src={url}
        alt={caption || "item"}
        className={`w-full rounded-lg ${
          selected &&
          "border border-indigo-100 border-[3px] shadow-[0px_2px_4px_-1px_#0000000F]"
        }`}
        width={220}
        height={124}
      />
    ) : (
      <div className="w-full py-2 pl-3 pr-8 border border-indigo-300 rounded-lg text-xs text-indigo-400">
        {text}
      </div>
    )}
    {caption && <span className="ml-1 text-xs text-[#A1A1AA]">{caption}</span>}
    <div className="p-1">
      <SvgIcon
        src={selected ? RadioOnIconSvg : RadioOffIconSvg}
        alt={selected ? "Selected" : "Not selected"}
        width={20}
        height={20}
      />
    </div>
  </div>
);
