import { FC, ChangeEvent } from "react";

interface TextboxProps {
  label?: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  helperText?: string;
  rows?: number;
}

export const Textbox: FC<TextboxProps> = ({
  label,
  value,
  onChange,
  placeholder,
  helperText,
  rows = 4,
}) => (
  <div className="mb-4">
    {label && (
      <label className="text-xs text-indigo-500 font-semibold mb-2 block">
        {label}
      </label>
    )}
    <textarea
      value={value}
      onChange={onChange}
      rows={rows}
      className="text-xs w-full border border-[#B2B2EF] rounded p-2 focus:ring-2 focus:ring-indigo-300 shadow-sm"
      placeholder={placeholder}
    />
    {helperText && <p className="text-xs text-[#A1A1AA]">{helperText}</p>}
  </div>
);
