"use client";

import React, { useRef, FC } from "react";
import { useFirebaseUpload } from "@/hooks/use-firebase-upload";
import { SvgIcon } from "@/components/common/svg-icon";
import UploadIcon from "@/assets/upload.svg";
import TrashIcon from "@/assets/trash-red.svg";
import { TbCopy } from "react-icons/tb";

interface SimpleFirebaseUploaderProps {
  storagePath?: string;
  useUUID?: boolean;
  onUploadComplete?: (
    result: {
      url: string;
      path: string;
      fileName: string;
    } | null
  ) => void;
  onError?: (error: Error) => void;
  accept?: string;
  label?: string;
  className?: string;
  value?: {
    url: string;
    path: string;
    fileName: string;
  } | null;
}

const SimpleFirebaseUploader: FC<SimpleFirebaseUploaderProps> = ({
  storagePath = "uploads",
  useUUID = true,
  onUploadComplete,
  onError,
  accept = "image/*",
  label,
  className = "",
  value,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const { upload, deleteFile, uploading, progress, error, result, reset } =
    useFirebaseUpload({
      path: storagePath,
      useUUID,
      onSuccess: onUploadComplete,
      onError,
    });

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await upload(file);
    }
  };

  const handleRemove = async () => {
    const fileData = result || value;
    if (fileData?.path) {
      const success = await deleteFile(fileData.path);
      if (success) {
        reset();
        if (inputRef.current) {
          inputRef.current.value = "";
        }
        // Notify parent to clear the value
        onUploadComplete?.(null);
      }
    }
  };

  // Use either the result from upload or the provided value
  const displayFile = result || value;

  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      {label && (
        <span className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
        </span>
      )}

      {!result && !value ? (
        <label className="relative flex flex-col items-center justify-center w-full h-40 border-2 border-dashed border-indigo-300 rounded-lg cursor-pointer bg-gradient-to-r from-[#EAEAFB] to-[#D9EEFE] hover:bg-gray-100 transition">
          <input
            ref={inputRef}
            type="file"
            accept={accept}
            className="hidden"
            onChange={handleFileSelect}
            disabled={uploading}
          />

          {uploading ? (
            <div className="text-center">
              <div className="mb-2">Uploading...</div>
              <div className="w-48 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-indigo-500 transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <div className="mt-1 text-sm text-gray-600">
                {Math.round(progress)}%
              </div>
            </div>
          ) : (
            <>
              <SvgIcon
                src={UploadIcon}
                alt="Upload..."
                width={48}
                height={48}
              />
              <span className="text-indigo-500 text-sm leading-5 font-semibold">
                Upload your File
              </span>
              <span className="text-indigo-500 text-xs text-center">
                Click or drag file to upload
              </span>
            </>
          )}
        </label>
      ) : (
        <div>
          <div className="flex flex-col items-center justify-center w-full p-4 border-2 border-dashed border-indigo-300 rounded-lg bg-gradient-to-r from-[#EAEAFB] to-[#D9EEFE]">
            {accept.includes("image") && displayFile && (
              <>
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={displayFile.url}
                  alt="Uploaded"
                  className="rounded-xl max-h-48 mb-3"
                  style={{ objectFit: "contain" }}
                />
              </>
            )}
            {displayFile && (
              <button
                type="button"
                onClick={() => {
                  navigator.clipboard.writeText(displayFile.url);
                  // Optional: Add a toast notification here
                }}
                className="flex items-center gap-1 text-indigo-600 hover:text-indigo-700 transition-colors"
                title="Copy URL"
              >
                <TbCopy className="w-4 h-4" />
              </button>
            )}
          </div>
          <button
            type="button"
            onClick={handleRemove}
            className="flex items-center gap-1 mt-3 text-xs leading-5 font-semibold text-[#F31260] cursor-pointer hover:underline"
          >
            <SvgIcon src={TrashIcon} alt="trash" width={16} height={16} />
            Remove
          </button>
        </div>
      )}

      {error && (
        <div className="mt-2 text-xs text-red-600">Error: {error.message}</div>
      )}
    </div>
  );
};

export default SimpleFirebaseUploader;
