import Image from "next/image";
import { CSSProperties } from "react";

interface SvgIconProps {
  src: string;
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  style?: CSSProperties;
}

export function SvgIcon({
  src,
  alt = "icon",
  className = "",
  width = 24,
  height = 24,
  style,
}: SvgIconProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      style={style}
    />
  );
}
