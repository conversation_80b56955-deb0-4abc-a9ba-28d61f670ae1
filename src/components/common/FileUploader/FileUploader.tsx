import { FC } from "react";
import Image from "next/image";
import { SvgIcon } from "@/components/common/svg-icon";
import UploadIcon from "@/assets/upload.svg";
import TrashIcon from "@/assets/trash-red.svg";

type UploadedFile = {
  id: string; // Cloudinary public_id
  url: string; // Cloudinary secure_url
};

interface Props {
  value: UploadedFile | null;
  onChange: (file: UploadedFile | null) => void;
  label?: string;
  accept?: string;
}

const FileUploader: FC<Props> = ({
  value,
  onChange,
  label,
  accept = "image/*",
}) => {
  const handleUpload = async (selectedFile: File) => {
    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append(
      "upload_preset",
      process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET!
    );

    const res = await fetch(
      `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/upload`,
      {
        method: "POST",
        body: formData,
      }
    );

    const data = await res.json();

    if (data.secure_url && data.public_id) {
      onChange({
        id: data.public_id,
        url: data.secure_url,
      });
    }
  };

  const handleRemoveFromDisplay = () => {
    onChange(null);
  };

  return (
    <div className="flex flex-col gap-2">
      {label && (
        <span className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
        </span>
      )}

      {!value ? (
        <label className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed border-indigo-300 rounded-lg cursor-pointer bg-gradient-to-r from-[#EAEAFB] to-[#D9EEFE] hover:bg-gray-100 transition">
          <input
            type="file"
            accept={accept}
            className="hidden"
            onChange={e => e.target.files && handleUpload(e.target.files[0])}
          />
          <SvgIcon src={UploadIcon} alt="Upload..." width={48} height={48} />
          <span className="text-indigo-500 text-sm leading-5 font-semibold">
            Upload your Photo
          </span>
          <span className="text-indigo-500 text-xs text-center">
            High-quality image at least 1024×1024 px (JPG or PNG).
          </span>
        </label>
      ) : (
        <div>
          <div className="flex flex-col items-center justify-center w-full border-2 border-dashed border-indigo-300 rounded-lg bg-gradient-to-r from-[#EAEAFB] to-[#D9EEFE]">
            <Image
              src={value.url}
              alt="Uploaded"
              className="rounded-xl"
              width={300}
              height={200}
              style={{ objectFit: "contain" }}
            />
          </div>
          <span
            className="flex items-center gap-1 mt-3 text-xs leading-5 font-semibold text-[#F31260] cursor-pointer hover:underline"
            onClick={handleRemoveFromDisplay}
          >
            <SvgIcon src={TrashIcon} alt="trash" width={16} height={16} />
            Remove
          </span>
        </div>
      )}
    </div>
  );
};

export default FileUploader;
