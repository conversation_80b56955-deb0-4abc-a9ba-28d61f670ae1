import React, { ReactNode, useMemo } from "react";

interface ToolProps {
  icon: ReactNode;
  label: string;
  active: boolean;
  disabled?: boolean;
  onClick: () => void;
}

export const Tool: React.FC<ToolProps> = ({
  icon,
  label,
  active,
  disabled = false,
  onClick,
}) => {
  const iconClasses = useMemo(() => {
    const base =
      "w-12 h-12 flex items-center justify-center flex flex-col items-center rounded-2xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500";
    const activeStyles = "bg-gradient-to-br from-purple-500 to-indigo-500";
    const inactiveStyles = "bg-[#EAEAFB] text-indigo-600";
    const disabledStyles = disabled
      ? "bg-[#F4F4F5] cursor-not-allowed"
      : "cursor-pointer hover:bg-gradient-to-br hover:from-purple-500 hover:to-indigo-500";
    return `${base} ${disabledStyles} ${active ? activeStyles : inactiveStyles}`;
  }, [active, disabled]);

  const textClasses = useMemo(() => {
    const base = "text-xs font-semibold";
    const enabledStyles = "cursor-pointer text-indigo-500";
    const disabledStyles = "text-indigo-300 cursor-not-allowed";
    return `${base} ${disabled ? disabledStyles : enabledStyles}`;
  }, [disabled]);

  return (
    <button
      type="button"
      onClick={() => !disabled && onClick()}
      aria-pressed={active}
      className="flex flex-col items-center gap-2"
    >
      <div className={iconClasses}>{icon}</div>
      <span className={textClasses}>{label}</span>
    </button>
  );
};
