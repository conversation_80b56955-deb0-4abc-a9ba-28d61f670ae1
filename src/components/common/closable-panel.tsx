import { FC, ReactNode } from "react";
import { TabItem, Tabs } from "@/components/common/tabs";
import CloseIconSvg from "../../assets/close.svg";
import { SvgIcon } from "./svg-icon";
import styles from "./closable-panel.module.css";

export interface ClosablePanelProps {
  title: string;
  tabs?: TabItem[];
  activeTab?: string;
  onTabChange?: (id: string) => void;
  onClose: () => void;
  children: ReactNode;
}

export const ClosablePanel: FC<ClosablePanelProps> = ({
  title,
  tabs,
  activeTab = "",
  onTabChange,
  onClose,
  children,
}) => (
  <div
    className={`${styles["closable-panel-container"]} overflow-auto h-full w-64 bg-white border border-indigo-50 rounded-lg flex flex-col`}
  >
    <div className="flex justify-between items-center h-12 p-3 border-b border-indigo-50 relative">
      <h2 className="text-sm font-bold text-indigo-500">{title}</h2>
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-gray-600 hover:text-gray-900"
      >
        <SvgIcon src={CloseIconSvg} alt="Close" width={24} height={24} />
      </button>
    </div>
    {tabs && onTabChange && (
      <Tabs tabs={tabs} activeTab={activeTab} onTabChange={onTabChange} />
    )}
    {children}
  </div>
);
