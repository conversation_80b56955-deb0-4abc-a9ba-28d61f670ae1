import { ReactNode } from "react";
import { Button as <PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";

export interface ButtonProps {
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  children: ReactNode;
  className?: string;
  disabled?: boolean;
  isLoading?: boolean;
  isFullWidth?: boolean;
  onClick?: () => void;
  variant?:
    | "solid"
    | "faded"
    | "bordered"
    | "light"
    | "flat"
    | "ghost"
    | "shadow";
  [key: string]: unknown;
}

export default function Button({
  color = "default",
  children,
  className = "",
  disabled = false,
  isFullWidth = false,
  isLoading = false,
  onClick,
  variant = "solid",
  ...props
}: ButtonProps) {
  return (
    <HeroButton
      color={color}
      className={className}
      isDisabled={disabled}
      isLoading={isLoading}
      fullWidth={isFullWidth}
      onPress={onClick}
      variant={variant}
      {...props}
    >
      {children}
    </HeroButton>
  );
}
