import { render, screen, fireEvent } from "@testing-library/react";
import <PERSON><PERSON> from "./Button";
import { vi } from "vitest";

describe("Button component", () => {
  it("renders with children text", () => {
    render(<Button onClick={vi.fn()}>Click Me</Button>);
    expect(
      screen.getByRole("button", { name: /click me/i })
    ).toBeInTheDocument();
  });

  it("calls onClick when pressed", () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click</Button>);

    fireEvent.click(screen.getByRole("button", { name: /click/i }));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("is disabled when isDisabled is true", () => {
    const handleClick = vi.fn();
    render(
      <Button isDisabled onClick={handleClick}>
        Disabled
      </Button>
    );

    const button = screen.getByRole("button", { name: /disabled/i });
    expect(button).toBeDisabled();

    fireEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it("applies fullWidth when isFullWidth is true", () => {
    const { container } = render(
      <Button isFullWidth onClick={vi.fn}>
        Full Width
      </Button>
    );

    const button = container.querySelector("button");
    expect(button?.className).toMatch(/fullwidth/i);
  });

  it("renders in loading state", () => {
    render(<Button onClick={vi.fn()}>Loading</Button>);

    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("aria-busy", "true"); // or test visually if spinner appears
  });

  it("renders with correct color and variant", () => {
    render(
      <Button color="primary" onClick={vi.fn()}>
        Styled
      </Button>
    );

    const button = screen.getByRole("button", { name: /styled/i });
    expect(button.className).toMatch(/primary/i);
    expect(button.className).toMatch(/bordered/i);
  });
});
