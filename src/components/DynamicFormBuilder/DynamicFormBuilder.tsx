"use client";

import React, { useEffect, useMemo } from "react";
import { useF<PERSON>, Controller, FieldValues } from "react-hook-form";
// import { z } from 'zod';
// import { zodResolver } from '@hookform/resolvers/zod';
import { Select, SelectItem, Switch, Divider } from "@heroui/react";
import { debounce } from "lodash";
import Input from "@/components/common/Input/Input";
import Button from "@/components/Button/Button";
import Textarea from "@/components/common/Textarea/Textarea";
import SimpleFirebaseUploader from "@/components/common/SimpleFirebaseUploader/SimpleFirebaseUploader";

// Field type definitions
export type FieldType =
  | "text"
  | "email"
  | "number"
  | "password"
  | "textarea"
  | "select"
  | "switch"
  | "checkbox"
  | "radio"
  | "file"
  | "section"
  | "divider"
  | "hidden"
  | "custom";

export interface SelectOption {
  value: string;
  label: string;
  description?: string;
}

export interface FieldConfig {
  // Core properties
  name?: string;
  type: FieldType;
  label?: string;
  placeholder?: string;
  description?: string;

  // Validation & behavior
  required?: boolean;
  disabled?: boolean;
  hidden?: boolean;
  allowEmpty?: boolean; // For select fields - defaults to false for required fields

  // Type-specific options
  options?: SelectOption[];
  accept?: string; // for file inputs
  rows?: number; // for textarea
  defaultValue?: unknown;

  // File upload specific options
  storagePath?: string; // Firebase storage path
  useUUID?: boolean; // Use UUID for file names

  // Layout
  gridSpan?: "full" | "half" | "third";
  className?: string;
  containerClassName?: string;

  // Advanced
  renderCondition?: (values: Record<string, unknown>) => boolean;
  render?: (props: Record<string, unknown>) => React.ReactNode;
}

export interface SectionConfig {
  id: string;
  title?: string;
  description?: string;
  fields: string[];
  className?: string;
  renderCondition?: (values: Record<string, unknown>) => boolean;
}

export interface ButtonConfig {
  label?: string;
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  variant?:
    | "solid"
    | "bordered"
    | "light"
    | "flat"
    | "faded"
    | "shadow"
    | "ghost";
  className?: string;
  disabled?: boolean;
}

export interface DynamicFormBuilderProps {
  // Schema & Fields Configuration
  // schema: z.ZodSchema;
  fields: FieldConfig[];
  sections?: SectionConfig[];

  // Form Handling
  onSubmit: (data: FieldValues) => void | Promise<void>;
  defaultValues?: FieldValues;

  // UI Configuration
  layout?: "single" | "multi-column" | "grid" | "auto-grid" | "flex";
  submitButton?: ButtonConfig;
  className?: string;
  formClassName?: string;

  // Advanced Features
  onValuesChange?: (values: Record<string, unknown>) => void;
  validationMode?: "onBlur" | "onChange" | "onSubmit";
  debounceMs?: number;
}

export function DynamicFormBuilder({
  // schema,
  fields,
  sections,
  onSubmit,
  defaultValues = {},
  layout = "multi-column",
  submitButton = { label: "Submit", color: "primary" },
  className = "",
  formClassName = "",
  onValuesChange,
  validationMode = "onBlur",
  debounceMs = 500,
}: DynamicFormBuilderProps) {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    register,
    setValue,
    watch,
  } = useForm({
    defaultValues,
    mode: validationMode === "onSubmit" ? "onSubmit" : validationMode,
  });

  // Watch all values for conditional rendering and external sync
  const watchedValues = watch();

  // Debounced values change handler
  const debouncedOnValuesChange = useMemo(
    () =>
      debounce((values: Record<string, unknown>) => {
        if (onValuesChange) {
          onValuesChange(values);
        }
      }, debounceMs),
    [onValuesChange, debounceMs]
  );

  // Effect to handle values change
  useEffect(() => {
    if (onValuesChange) {
      const subscription = watch(values => {
        debouncedOnValuesChange(values);
      });
      return () => subscription.unsubscribe();
    }
    return undefined;
  }, [watch, debouncedOnValuesChange, onValuesChange]);

  // Get layout classes
  const getLayoutClasses = () => {
    switch (layout) {
      case "single":
        return "flex flex-col gap-4";
      case "grid":
        return "grid grid-cols-12 gap-4 auto-rows-auto grid-flow-dense";
      case "auto-grid":
        // Automatically adjust columns based on available space
        return "grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-4 auto-rows-auto";
      case "flex":
        // Flexbox layout that automatically adjusts field widths
        return "flex flex-wrap gap-4";
      case "multi-column":
      default:
        return "grid grid-cols-1 md:grid-cols-2 gap-4 auto-rows-auto grid-flow-dense";
    }
  };

  // Get grid span classes
  const getGridSpanClasses = (gridSpan?: string) => {
    // Handle flexbox layout
    if (layout === "flex") {
      switch (gridSpan) {
        case "full":
          return "w-full basis-full";
        case "third":
          return "flex-1 min-w-[280px] max-w-[33.333%]";
        case "half":
          return "flex-1 min-w-[280px] max-w-[50%]";
        default:
          return "flex-1 min-w-[280px]";
      }
    }

    // Handle grid layouts
    if (layout !== "grid") {
      return gridSpan === "full" ? "col-span-full" : "";
    }

    switch (gridSpan) {
      case "full":
        return "col-span-12";
      case "third":
        return "col-span-4";
      case "half":
      default:
        return "col-span-6";
    }
  };

  // Render individual field
  const renderField = (field: FieldConfig) => {
    // Check render condition
    if (field.renderCondition && !field.renderCondition(watchedValues)) {
      return null;
    }

    // Check if field is hidden
    if (field.hidden) {
      if (field.type === "hidden" && field.name) {
        return (
          <input
            key={field.name}
            {...register(field.name)}
            type="hidden"
            value={String(field.defaultValue || "")}
          />
        );
      }
      return null;
    }

    const error = field.name ? errors[field.name] : null;
    const containerClasses = `${getGridSpanClasses(field.gridSpan)} ${
      field.containerClassName || ""
    }`;

    switch (field.type) {
      case "section":
        return (
          <div key={`section-${field.label}`} className="col-span-full">
            {field.label && (
              <h3 className="text-lg font-semibold mb-2">{field.label}</h3>
            )}
            {field.description && (
              <p className="text-sm text-gray-600 mb-4">{field.description}</p>
            )}
          </div>
        );

      case "divider":
        return (
          <div key={`divider-${field.label}`} className="col-span-full">
            <Divider className="my-4" />
          </div>
        );

      case "text":
      case "email":
      case "password":
      case "number":
        return (
          <div key={field.name} className={containerClasses}>
            <Input
              name={field.name}
              control={control}
              type={field.type}
              label={field.label}
              placeholder={field.placeholder}
              isDisabled={field.disabled}
              isRequired={field.required}
              errorMessage={error?.message as string}
              isInvalid={!!error}
              description={field.description}
              className={field.className}
            />
          </div>
        );

      case "textarea":
        return (
          <div key={field.name} className={containerClasses}>
            <Textarea
              name={field.name}
              control={control}
              label={field.label}
              placeholder={field.placeholder}
              isDisabled={field.disabled}
              isRequired={field.required}
              errorMessage={error?.message as string}
              isInvalid={!!error}
              description={field.description}
              className={field.className}
            />
          </div>
        );

      case "select":
        return field.name ? (
          <div key={field.name} className={containerClasses}>
            <Controller
              name={field.name}
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Select
                  label={field.label}
                  placeholder={field.placeholder}
                  selectedKeys={value ? [value] : []}
                  onChange={e => onChange(e.target.value)}
                  onBlur={onBlur}
                  isDisabled={field.disabled}
                  isRequired={field.required}
                  errorMessage={error?.message as string}
                  isInvalid={!!error}
                  description={field.description}
                  className={field.className}
                  disallowEmptySelection={
                    field.allowEmpty === false ||
                    (field.required && field.allowEmpty !== true)
                  }
                >
                  {(field.options || []).map(option => (
                    <SelectItem key={option.value}>{option.label}</SelectItem>
                  ))}
                </Select>
              )}
            />
          </div>
        ) : null;

      case "switch":
      case "checkbox":
        return field.name ? (
          <div key={field.name} className={containerClasses}>
            <Controller
              name={field.name}
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <Switch
                  isSelected={value || false}
                  onValueChange={onChange}
                  onBlur={onBlur}
                  isDisabled={field.disabled}
                  className={field.className}
                >
                  {field.label}
                  {field.required && (
                    <span className="text-danger ml-1">*</span>
                  )}
                </Switch>
              )}
            />
            {field.description && (
              <p className="text-sm text-gray-500 mt-1">{field.description}</p>
            )}
            {error && (
              <p className="text-sm text-danger mt-1">
                {error.message as string}
              </p>
            )}
          </div>
        ) : null;

      case "radio":
        return field.name ? (
          <div key={field.name} className={containerClasses}>
            {field.label && (
              <label className="block text-sm font-medium mb-2">
                {field.label}
                {field.required && <span className="text-danger ml-1">*</span>}
              </label>
            )}
            <Controller
              name={field.name}
              control={control}
              render={({ field: { onChange, value } }) => (
                <div className="space-y-2">
                  {field.options?.map(option => (
                    <label
                      key={option.value}
                      className="flex items-center cursor-pointer"
                    >
                      <input
                        type="radio"
                        value={option.value}
                        checked={value === option.value}
                        onChange={e => onChange(e.target.value)}
                        disabled={field.disabled}
                        className="mr-2"
                      />
                      <span className="text-sm">{option.label}</span>
                    </label>
                  ))}
                </div>
              )}
            />
            {field.description && (
              <p className="text-sm text-gray-500 mt-1">{field.description}</p>
            )}
            {error && (
              <p className="text-sm text-danger mt-1">
                {error.message as string}
              </p>
            )}
          </div>
        ) : null;

      case "file":
        return field.name ? (
          <div key={field.name} className={containerClasses}>
            <Controller
              name={field.name}
              control={control}
              render={({ field: { value, onChange } }) => (
                <SimpleFirebaseUploader
                  label={field.label}
                  value={value}
                  storagePath={field.storagePath || "uploads"}
                  useUUID={field.useUUID !== false}
                  onUploadComplete={result => {
                    // Store the complete upload result with URL
                    onChange(
                      result
                        ? {
                            url: result.url,
                            path: result.path,
                            fileName: result.fileName,
                          }
                        : null
                    );
                  }}
                  onError={error => {
                    console.error("Upload error:", error);
                  }}
                  accept={field.accept}
                  className={field.className}
                />
              )}
            />
            {field.description && (
              <p className="text-sm text-gray-500 mt-1">{field.description}</p>
            )}
            {error && (
              <p className="text-sm text-danger mt-1">
                {error.message as string}
              </p>
            )}
          </div>
        ) : null;

      case "custom":
        return field.render && field.name ? (
          <div key={field.name} className={containerClasses}>
            {field.render({ register, control, errors, setValue, watch })}
          </div>
        ) : null;

      default:
        return null;
    }
  };

  // Render fields by section
  const renderFieldsBySection = () => {
    if (!sections || sections.length === 0) {
      // No sections defined, render all fields
      return fields.map(renderField);
    }

    // Render fields organized by sections
    return sections.map(section => {
      // Check section render condition
      if (section.renderCondition && !section.renderCondition(watchedValues)) {
        return null;
      }

      const sectionFields = fields.filter(field =>
        field.name ? section.fields.includes(field.name) : false
      );

      return (
        <div
          key={section.id}
          className={`col-span-full ${section.className || ""}`}
        >
          {section.title && (
            <h3 className="text-lg font-semibold mb-2">{section.title}</h3>
          )}
          {section.description && (
            <p className="text-sm text-gray-600 mb-4">{section.description}</p>
          )}
          <div className={getLayoutClasses()}>
            {sectionFields.map(renderField)}
          </div>
        </div>
      );
    });
  };

  return (
    <div className={className}>
      <form onSubmit={handleSubmit(onSubmit)} className={formClassName}>
        <div className={getLayoutClasses()}>
          {sections ? renderFieldsBySection() : fields.map(renderField)}
        </div>
        <div className="mt-6">
          <Button
            type="submit"
            color={submitButton.color}
            variant={submitButton.variant}
            isLoading={isSubmitting}
            isDisabled={submitButton.disabled || isSubmitting}
            className={submitButton.className}
          >
            {submitButton.label}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default DynamicFormBuilder;
