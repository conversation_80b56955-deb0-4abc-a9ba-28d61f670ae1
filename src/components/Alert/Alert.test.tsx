import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import Alert from "./Alert";
import type { AlertColor } from "./Alert";

describe("Alert", () => {
  const defaultProps = {
    title: "Test Title",
    description: "Test description.",
    color: "primary" as AlertColor,
    isVisible: true,
    onClose: vi.fn(),
  };

  it("renders the alert with title and description when visible", () => {
    render(<Alert {...defaultProps} />);
    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Test description.")).toBeInTheDocument();
  });

  it("does not render when isVisible is false", () => {
    render(<Alert {...defaultProps} isVisible={false} />);
    expect(screen.queryByText("Test Title")).not.toBeInTheDocument();
  });

  it("calls onClose when the close button is clicked", () => {
    render(<Alert {...defaultProps} />);
    const closeButton = screen.getByRole("button");
    fireEvent.click(closeButton);
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it("renders without description if not provided", () => {
    render(<Alert {...defaultProps} description={undefined} />);
    expect(screen.queryByText("Test description.")).not.toBeInTheDocument();
  });
});
