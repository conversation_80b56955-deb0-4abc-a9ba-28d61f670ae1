import {
  Alert as HeroAlert,
  AlertProps as HeroAlertProps,
} from "@heroui/react";

export type AlertColor =
  | "default"
  | "primary"
  | "secondary"
  | "success"
  | "warning"
  | "danger";

export interface AlertProps extends Omit<HeroAlertProps, "variant"> {
  color: AlertColor;
  title: string;
  description?: string;
  isVisible: boolean;
  onClose: () => void;
}

export default function Alert({
  title,
  description,
  className,
  isVisible,
  onClose,
  ...props
}: AlertProps) {
  if (!isVisible) return null;

  return (
    <HeroAlert
      hideIconWrapper
      isClosable={true}
      onClose={onClose}
      variant="faded"
      className={className}
      {...props}
    >
      <div className="flex justify-between items-start gap-2">
        <div className="flex flex-col">
          <h3 className="font-semibold text-sm">{title}</h3>
          {description && (
            <p className="text-sm text-gray-700">{description}</p>
          )}
        </div>
      </div>
    </HeroAlert>
  );
}
