import { FC } from "react";
import { TimelineHeader } from "@/components/storyboard/timeline-header";
import { Track } from "@/components/timeline/use-timeline-store";
import { TimelineEmpty } from "@/components/storyboard/timeline-empty";
import { TimelineRuler } from "@/components/storyboard/timeline-ruler";
import { TimelineTrack } from "@/components/storyboard/timeline-track";

export interface TimelineProps {
  tracks: Track[];
}

export const Timeline: FC<TimelineProps> = ({ tracks = [] }) => {
  return (
    <div className="h-full box-border mx-4 p-4 bg-white rounded-2xl shadow-[0px_1px_2px_-1px_#0000001A]">
      <TimelineHeader />
      <TimelineRuler />
      {tracks.length === 0 ? (
        <TimelineEmpty />
      ) : (
        <div className="overflow-x-auto">
          <div className="relative min-w-[600px]">
            {tracks.map((track: Track) => (
              <TimelineTrack
                key={track.id}
                id={track.id}
                title={track.title}
                type={track.mediaType}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
