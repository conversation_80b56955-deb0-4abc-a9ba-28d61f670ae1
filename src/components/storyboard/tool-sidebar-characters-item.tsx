import { FC } from "react";
import Image from "next/image";
import { Character } from "@/components/storyboard/tool-sidebar-characters";

export const ToolSidebarCharactersItem: FC<Character> = ({
  name,
  handle,
  avatarUrl,
}) => {
  return (
    <div className="h-[57px] flex items-center gap-2 border border-[#EDEBFF] rounded-lg px-3 py-2 bg-white shadow-sm cursor-pointer hover:bg-[#EAEAFB]">
      <Image
        src={avatarUrl}
        alt={name}
        className="rounded-full object-cover"
        width={48}
        height={48}
      />
      <div className="flex flex-col">
        <p className="text-sm font-semibold leading-5 text-[#4A3AFF]">{name}</p>
        <p className="text-sm text-[#4A3AFF]">{handle}</p>
      </div>
    </div>
  );
};
