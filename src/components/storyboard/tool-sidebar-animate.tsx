import { FC, useCallback, useState } from "react";
import { TabItem } from "@/components/common/tabs";
import { ClosablePanel } from "@/components/common/closable-panel";
import { Textbox } from "@/components/common/textbox";
import { Button } from "@/components/common/button";
import { ToolStepMessage } from "@/components/storyboard/tool-step-message";
import { SelectableItemsList } from "@/components/common/selectable-items-list";
import ShotPlaceholder from "@/assets/shot-placeholder.jpg";

export interface Animation {
  id: string;
  imageUrl: string;
}

export interface ToolSidebarAnimateProps {
  active: boolean;
  onClose: () => void;
}

export const ToolSidebarAnimate: FC<ToolSidebarAnimateProps> = ({
  active,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<string>("create");
  const [description, setDescription] = useState("");
  const [selected, setSelected] = useState<string | null>(null);
  const [animations, setAnimations] = useState<Animation[]>([]);

  const addAnimation = useCallback(() => {
    setAnimations([
      ...animations,
      { id: Math.random().toString(), imageUrl: ShotPlaceholder.src },
    ]);
    setDescription("");
  }, [animations]);

  if (!active) return null;

  const tabItems: TabItem[] = [
    { id: "create", label: "Animate shot" },
    { id: "list", label: "Animations", count: animations.length },
  ];

  return (
    <ClosablePanel
      title="Animate"
      tabs={tabItems}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      onClose={onClose}
    >
      {activeTab === "create" ? (
        <div className="flex flex-col px-4 py-6 h-[466px] overflow-y-auto">
          <Textbox
            label="Describe action"
            value={description}
            onChange={e => setDescription(e.target.value)}
            placeholder="Type how this shot should animate..."
            helperText="Start typing @ to bring up your characters."
            rows={6}
          />
          <Button
            title="Create animation"
            variant="primary"
            disabled={!description}
            onClick={addAnimation}
          />
        </div>
      ) : (
        <>
          {animations.length === 0 ? (
            <ToolStepMessage
              title="No animations yet!"
              message="They’ll show up here once you start animating your shots."
            />
          ) : (
            <SelectableItemsList
              items={animations}
              selected={selected}
              setSelected={setSelected}
            />
          )}
        </>
      )}
    </ClosablePanel>
  );
};
