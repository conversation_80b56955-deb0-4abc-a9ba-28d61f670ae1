import { FC } from "react";
import AnimateIcon from "../../assets/animate.svg";
import VideoIcon from "../../assets/video.svg";
import { SvgIcon } from "../common/svg-icon";

declare interface HeaderProps {
  title: string;
}

export const StoryboardHeader: FC<HeaderProps> = ({ title }) => {
  return (
    <div className=" border-indigo-50 border-b h-17 px-5 bg-white mb-4">
      <div className="h-full flex items-center gap-4">
        <div className="flex-1 text-base font-bold text-indigo-500">
          {title}
        </div>
        <button className="flex items-center gap-2 text-xs font-semibold text-indigo-300 hover:text-gray-900 border border-indigo-100 rounded-lg px-3 py-2">
          <SvgIcon src={AnimateIcon} alt="Animate" width={20} height={20} />
          Animate all shots
        </button>
        <button className="flex items-center gap-3 text-xs font-semibold text-indigo-300 hover:text-gray-900  rounded-lg px-3 py-2 bg-gray-100">
          <SvgIcon
            className="text-indigo-300"
            src={VideoIcon}
            alt="Video"
            width={20}
            height={20}
          />
          Preview your cartoon
        </button>
      </div>
    </div>
  );
};
