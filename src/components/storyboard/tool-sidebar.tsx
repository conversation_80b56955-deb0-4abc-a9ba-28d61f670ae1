import { FC, ReactNode } from "react";
import { Tool as Too<PERSON><PERSON><PERSON><PERSON> } from "@/components/common/tool";
import { SidebarTab } from "@/stores";

export interface Tool {
  value: SidebarTab;
  label: string;
  icon: ReactNode;
  disabled?: boolean;
}

export interface ToolSidebarProps {
  tools: Tool[];
  activeTool: SidebarTab;
  setActiveTool: (tool: SidebarTab) => void;
}

export const ToolSidebar: FC<ToolSidebarProps> = ({
  tools,
  activeTool,
  setActiveTool,
}) => (
  <div className="flex overflow-x-auto scrollbar-hide">
    <div className="my-auto">
      <div className="flex flex-col items-center space-y-4">
        {tools.map(tool => (
          <ToolButton
            key={tool.value}
            icon={tool.icon}
            label={tool.label}
            active={activeTool === tool.value}
            disabled={tool.disabled}
            onClick={() => setActiveTool(tool.value)}
          />
        ))}
      </div>
    </div>
  </div>
);
