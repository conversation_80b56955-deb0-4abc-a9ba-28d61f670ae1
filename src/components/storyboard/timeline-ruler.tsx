function formatTime(seconds: number): string {
  const m = Math.floor(seconds / 60)
    .toString()
    .padStart(1, "0");
  const s = (seconds % 60).toString().padStart(2, "0");
  return `${m}:${s}`;
}

export const TimelineRuler = () => {
  const duration = 65;
  const markInterval = 5;

  return (
    <div className="flex items-end h-8 border-b border-[#EAEAFB]">
      <div className="flex-1 flex">
        {Array.from({ length: duration + 1 }).map((_, i) => (
          <div
            key={i}
            className="flex-1 flex flex-col items-center justify-center"
          >
            {i > 0 && i % markInterval === 0 ? (
              <div className="text-xs text-indigo-300">{formatTime(i)}</div>
            ) : (
              <div className="w-px h-2 bg-indigo-300" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
