"use client";
import { FC, useCallback, useState } from "react";
import { TabItem } from "@/components/common/tabs";
import { ClosablePanel } from "@/components/common/closable-panel";
import { Textbox } from "@/components/common/textbox";
import { <PERSON><PERSON> } from "@/components/common/button";
import { Dropdown, DropdownOption } from "@/components/common/dropdown";
import { SvgIcon } from "../common/svg-icon";
import PurpIcon from "../../assets/voices/purp.svg";
import FuzzyIcon from "../../assets/voices/fuzzy.svg";
import ZigIcon from "../../assets/voices/zig.svg";
import { ToolStepMessage } from "@/components/storyboard/tool-step-message";
import { SelectableItemsList } from "@/components/common/selectable-items-list";
import moment from "moment";
import {
  MediaType,
  TrackItem,
  TrackType,
} from "@/components/timeline/use-timeline-store";

export interface Narration {
  id: string;
  text: string;
  creationDate: Date;
}

export interface ToolSidebarNarratorProps {
  active: boolean;
  onClose: () => void;
  addTrack: (
    type: TrackType,
    mediaType: MediaType,
    title: string,
    purpose: string,
    items: TrackItem[]
  ) => void;
}

const voices: DropdownOption[] = [
  {
    id: "purp",
    label: "Purp",
    description: "Playful, cheerful, a bit goofy",
    icon: <SvgIcon src={PurpIcon} alt="Purp" />,
  },
  {
    id: "fuzzy",
    label: "Fuzzy",
    description: "Soft-spoken, calming, thoughtful",
    icon: <SvgIcon src={FuzzyIcon} alt="Fuzzy" />,
  },
  {
    id: "zig",
    label: "Zig",
    description: "Confident, adventurous, storybook style",
    icon: <SvgIcon src={ZigIcon} alt="Zig" />,
  },
];

export const ToolSidebarNarrator: FC<ToolSidebarNarratorProps> = ({
  active,
  onClose,
  addTrack,
}) => {
  const [activeTab, setActiveTab] = useState<string>("create");
  const [script, setScript] = useState("");
  const [voice, setVoice] = useState("");
  const [selected, setSelected] = useState<string | null>(null);
  const [narrations, setNarrations] = useState<Narration[]>([]);

  const addNarration = useCallback(() => {
    setNarrations([
      ...narrations,
      { id: Math.random().toString(), text: script, creationDate: new Date() },
    ]);
    addTrack("default", "narration", `${voice} Speaking`, "", []);
    setScript("");
    setVoice("");
  }, [addTrack, narrations, script, voice]);

  if (!active) return null;

  const tabItems: TabItem[] = [
    { id: "create", label: "Create Narration" },
    { id: "list", label: "Narrations", count: narrations.length },
  ];

  return (
    <ClosablePanel
      title="Narrator"
      tabs={tabItems}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      onClose={onClose}
    >
      <div className="px-4 py-6">
        {activeTab === "create" ? (
          <div className="flex-1 flex flex-col h-[466px] overflow-auto">
            <Textbox
              label="Narrator’s script"
              value={script}
              onChange={e => setScript(e.target.value)}
              placeholder="Write what the narrator should say for this story..."
              rows={6}
            />
            <Dropdown
              value={voice}
              options={voices}
              placeholder="Select a narrator voice"
              label="Narrator Voice"
              onChange={(voice: string) => setVoice(voice)}
            />
            <Button
              title="Create narration"
              variant="primary"
              disabled={!script || !voice}
              onClick={addNarration}
            />
          </div>
        ) : (
          <>
            {narrations.length === 0 ? (
              <ToolStepMessage
                title="Nothing here yet!"
                message="Generate a voice, and I’ll store it for future use."
              />
            ) : (
              <SelectableItemsList
                items={narrations.map(({ id, text, creationDate }) => ({
                  id,
                  text,
                  caption: `Created on ${moment(creationDate).format(
                    "MMMM D, YYYY"
                  )}`,
                }))}
                selected={selected}
                setSelected={setSelected}
              />
            )}
          </>
        )}
      </div>
    </ClosablePanel>
  );
};
