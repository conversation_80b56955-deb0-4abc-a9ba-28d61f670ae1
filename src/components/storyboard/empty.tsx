import PurbLeftPNG from "@/assets/purp-left.png";
import { InfoBubble } from "@/components/common/info-bubble";
import Image from "next/image";

export const Empty = () => {
  return (
    <div className="relative overflow-hidden w-[930px] h-[523px] shadow-[0px_1px_2px_0px_#0000000D] bg-gradient-to-r from-[#FFE0FD] to-[#D8D8F7] rounded-lg">
      <div className="absolute left-[246px] top-[145px] w-[299px]">
        <InfoBubble
          title="Blank shot, big potential."
          details="Describe the scene, and we’ll build something great together."
        />
      </div>
      <Image
        src={PurbLeftPNG.src}
        alt="purp..."
        className="absolute bottom-0 left-0"
        width={397}
        height={476}
      />
    </div>
  );
};
