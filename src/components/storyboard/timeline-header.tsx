import { FC } from "react";
import PlayIcon from "../../assets/play.svg";
import PencilIcon from "../../assets/pencil.svg";
import SplitIcon from "../../assets/split.svg";
import TrashIcon from "../../assets/trash.svg";
import { SvgIcon } from "../common/svg-icon";

export const TimelineHeader: FC = () => {
  return (
    <div className="h-14 bg-white border-b border-gray-200 px-8 flex items-center justify-between">
      <h3 className="text-xl font-bold text-black">Timeline</h3>
      <div className="flex gap-6">
        <button className="flex items-center gap-3 text-sm font-semibold text-gray-700 hover:text-gray-900">
          <SvgIcon src={PlayIcon} alt="Play" width={16} height={16} />
          Preview
        </button>
        <button className="flex items-center gap-3 text-sm font-semibold text-gray-700 hover:text-gray-900">
          <SvgIcon src={PencilIcon} alt="Edit" width={16} height={16} />
          Edit
        </button>
        <button className="flex items-center gap-3 text-sm font-semibold text-gray-700 hover:text-gray-900">
          <SvgIcon src={SplitIcon} alt="Split" width={16} height={16} />
          Split
        </button>
        <button className="flex items-center gap-3 text-sm font-semibold text-gray-700 hover:text-gray-900">
          <SvgIcon src={TrashIcon} alt="Delete" width={16} height={16} />
          Delete
        </button>
      </div>
    </div>
  );
};
