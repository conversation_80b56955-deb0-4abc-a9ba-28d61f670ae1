import { FC, useCallback, useState } from "react";
import { ClosablePanel } from "@/components/common/closable-panel";
import { Button } from "@/components/common/button";
import { Dropdown, DropdownOption } from "@/components/common/dropdown";
import {
  MediaType,
  TrackItem,
  TrackType,
} from "@/components/timeline/use-timeline-store";

export interface ToolSidebarNarratorProps {
  active: boolean;
  onClose: () => void;
  addTrack: (
    type: TrackType,
    mediaType: MediaType,
    title: string,
    purpose: string,
    items: TrackItem[]
  ) => void;
}

const audios: DropdownOption[] = [
  {
    id: "Calm currents",
    label: "Calm currents",
    description: "Soft ambient tones with subtle piano",
  },
  {
    id: "Studio buzz",
    label: "Studio buzz",
    description: "Warm lo-fi beat with mellow synths",
  },
  {
    id: "Momentum",
    label: "Momentum",
    description: "Upbeat electronic rhythm with light percussion",
  },
];

export const ToolSidebarAudio: FC<ToolSidebarNarratorProps> = ({
  active,
  onClose,
  addTrack,
}) => {
  const [audio, setAudio] = useState("");

  const addAudio = useCallback(() => {
    setAudio("");
    addTrack("default", "audio", audio, "", []);
  }, [addTrack, audio]);

  if (!active) return null;

  return (
    <ClosablePanel title="Audio" onClose={onClose}>
      <div className="flex-1 flex flex-col px-4 py-6">
        <Dropdown
          value={audio}
          options={audios}
          placeholder="Choose a track "
          label="Select background audio"
          onChange={(audio: string) => setAudio(audio)}
        />
        <Button
          title="Add background audio"
          variant="primary"
          disabled={!audio}
          onClick={addAudio}
        />
      </div>
    </ClosablePanel>
  );
};
