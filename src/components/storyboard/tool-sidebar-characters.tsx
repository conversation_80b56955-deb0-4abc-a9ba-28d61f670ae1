import { FC, useEffect, useState } from "react";
import { ClosablePanel } from "@/components/common/closable-panel";
import { Button } from "@/components/common/button";
import CreateCharacterModal from "@/components/CreateCharacterModal/CreateCharacterModal";
import { SvgIcon } from "@/components/common/svg-icon";
import CharacterIcon from "@/assets/character.svg";
import { ToolSidebarCharactersItem } from "@/components/storyboard/tool-sidebar-characters-item";
import { ToolStepMessage } from "@/components/storyboard/tool-step-message";

export interface Character {
  name: string;
  handle: string;
  avatarUrl: string;
}

export interface ToolSidebarCharactersProps {
  active: boolean;
  onClose: () => void;
}

export const ToolSidebarCharacters: FC<ToolSidebarCharactersProps> = ({
  active,
  onClose,
}) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [characters] = useState<Character[]>([]);

  useEffect(() => {
    /*setCharacters([
      {
        name: "<PERSON>",
        handle: "@alice",
        avatarUrl: "https://png.pngtree.com/png-vector/20241119/ourmid/pngtree-cute-cartoon-character-face-for-children-s-entertainment-png-image_14497307.png",
      },
      {
        name: "Bob",
        handle: "@bob",
        avatarUrl: "https://png.pngtree.com/png-vector/20240715/ourmid/pngtree-happy-cartoon-character-boy-png-image_13113225.png",
      },
      {
        name: "Charlie",
        handle: "@charlie",
        avatarUrl: "https://www.pikpng.com/pngl/m/371-3718162_3d-character-png-toon-character-png-clipart.png",
      },
    ]);*/
  }, []);

  if (!active) return null;

  return (
    <>
      <ClosablePanel title="Characters" onClose={onClose}>
        <div className="px-4 py-6">
          {characters.length > 0 && (
            <div className="flex flex-col gap-2 mb-11">
              {characters.map((profile, index) => (
                <ToolSidebarCharactersItem
                  key={index}
                  name={profile.name}
                  handle={profile.handle}
                  avatarUrl={profile.avatarUrl}
                />
              ))}
            </div>
          )}
          <Button
            icon={<SvgIcon src={CharacterIcon} alt="Characters" />}
            title="Create new character"
            variant="outline"
            onClick={() => {
              setModalOpen(true);
            }}
          />
        </div>
        {characters.length === 0 && (
          <ToolStepMessage
            title="Nothing here yet!"
            message="Generate a character and I’ll store in the library for future use."
          />
        )}
      </ClosablePanel>
      <CreateCharacterModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
      />
    </>
  );
};
