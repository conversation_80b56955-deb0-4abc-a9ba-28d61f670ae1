import { FC, useCallback, useState } from "react";
import { TabItem } from "@/components/common/tabs";
import { ClosablePanel } from "@/components/common/closable-panel";
import { Toggle } from "@/components/common/toggle";
import { Textbox } from "@/components/common/textbox";
import { Button } from "@/components/common/button";
import { ToolStepMessage } from "@/components/storyboard/tool-step-message";
import { SelectableItemsList } from "@/components/common/selectable-items-list";
import ShotPlaceholder from "../../assets/shot-placeholder.jpg";
import {
  MediaType,
  TrackItem,
  TrackType,
} from "@/components/timeline/use-timeline-store";

export interface Shot {
  id: string;
  imageUrl: string;
}

export interface ToolSidebarShotsProps {
  active: boolean;
  onClose: () => void;
  addTrack: (
    type: TrackType,
    mediaType: MediaType,
    title: string,
    purpose: string,
    items: TrackItem[]
  ) => void;
}

export const ToolSidebarShots: FC<ToolSidebarShotsProps> = ({
  active,
  onClose,
  addTrack,
}) => {
  const [activeTab, setActiveTab] = useState<string>("create");
  const [description, setDescription] = useState("");
  const [editDetail, setEditDetail] = useState(false);
  const [detailText, setDetailText] = useState("");
  const [selected, setSelected] = useState<string | null>(null);
  const [shots, setShots] = useState<Shot[]>([]);

  const addShot = useCallback(() => {
    setShots([
      ...shots,
      { id: Math.random().toString(), imageUrl: ShotPlaceholder.src },
    ]);
    addTrack("default", "video", "", "", []);
    setDescription("");
    setEditDetail(false);
    setDetailText("");
  }, [shots, addTrack]);

  if (!active) return null;

  const tabItems: TabItem[] = [
    { id: "create", label: "Create shot" },
    { id: "list", label: "Shots", count: shots.length },
  ];

  return (
    <ClosablePanel
      title="Shots"
      tabs={tabItems}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      onClose={onClose}
    >
      <div className="px-4 py-6">
        {activeTab === "create" ? (
          <div className="flex flex-col h-[466px] overflow-y-auto">
            <Textbox
              label="Shot description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Describe the shot! What's happening, where, and who's in it... Use @ to tag characters."
              helperText="Start typing @ to bring up your characters."
              rows={6}
            />
            <Toggle
              checked={editDetail}
              onChange={setEditDetail}
              label="Edit shot detail"
            />
            {editDetail && (
              <Textbox
                value={detailText}
                onChange={e => setDetailText(e.target.value)}
                placeholder="Describe what you'd like to change..."
                rows={4}
              />
            )}
            <Button
              title="Create a new shot"
              variant="primary"
              disabled={!description}
              onClick={addShot}
            />
          </div>
        ) : (
          <>
            {shots.length === 0 ? (
              <ToolStepMessage
                title="No shots yet!"
                message="They’ll show up here once you start creating."
              />
            ) : (
              <SelectableItemsList
                items={shots}
                selected={selected}
                setSelected={setSelected}
              />
            )}
          </>
        )}
      </div>
    </ClosablePanel>
  );
};
