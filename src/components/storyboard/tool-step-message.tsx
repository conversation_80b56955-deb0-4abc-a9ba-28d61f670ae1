import { FC } from "react";
import { InfoBubble } from "@/components/common/info-bubble";
import PurbDownPNG from "@/assets/purp-down.png";
import Image from "next/image";

export interface ToolStepMessageProps {
  title: string;
  message: string;
}
export const ToolStepMessage: FC<ToolStepMessageProps> = ({
  title,
  message,
}) => {
  return (
    <div className="pt-44 px-4 relative h-[466px]">
      <InfoBubble title={title} details={message} />
      <Image
        src={PurbDownPNG.src}
        alt="purp..."
        className="absolute bottom-0 right-0"
        width={181}
        height={205}
      />
    </div>
  );
};
