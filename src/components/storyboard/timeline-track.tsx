import { FC, useMemo } from "react";
import AudioIconSvg from "../../assets/audio.svg";
import NarrationIconSvg from "../../assets/narrate.svg";
import EmptyBgSvg from "../../assets/empty-bg.svg";
import { SvgIcon } from "../common/svg-icon";
import { TimelineDragHandle } from "@/components/storyboard/timeline-drag-handle";
import { MediaType } from "@/components/timeline/use-timeline-store";

export interface AudioTrackProps {
  id: string;
  title: string;
  type: MediaType;
}

export const TimelineTrack: FC<AudioTrackProps> = ({ title, type }) => {
  const containerBackground = useMemo(
    () =>
      type === "video"
        ? "bg-[linear-gradient(90deg,_#F3ECF9_0%,_#EAEAFB_88.99%)]"
        : "",
    [type]
  );

  const background = useMemo(
    () =>
      type === "audio"
        ? "bg-[#B2DCFC]"
        : type === "video"
          ? "bg-white"
          : "bg-indigo-200",
    [type]
  );

  const width = useMemo(() => (type === "video" ? "w-29" : ""), [type]);

  const icon =
    type === "audio" ? (
      <SvgIcon src={AudioIconSvg} alt="Audio" width={16} height={16} />
    ) : (
      <SvgIcon src={NarrationIconSvg} alt="Narration" width={16} height={16} />
    );

  return (
    <div
      className={`h-14 p-2 border-b border-indigo-50 ${containerBackground}`}
    >
      <div
        className={`h-10 rounded-lg ${background} ${width} text-indigo-500 border border-indigo-50 flex items-center shadow-[0px_1px_2px_0px_#3E3ED866] h-10`}
      >
        <div className={`flex items-center gap-[6px] p-1 ${width}`}>
          <TimelineDragHandle />
          {type === "video" ? (
            <SvgIcon
              src={EmptyBgSvg}
              alt="Empty background"
              width={100}
              height={60}
            />
          ) : (
            <>
              {icon}
              <div className="text-xs text-indigo-500 leading-4 font-medium min-w-[120px]">
                {title}
              </div>
            </>
          )}
          <TimelineDragHandle />
        </div>
      </div>
    </div>
  );
};
