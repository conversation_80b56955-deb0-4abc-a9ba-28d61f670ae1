import { render, screen, fireEvent } from "@testing-library/react";
import ButtonGroup from "./ButtonGroup";
import { vi } from "vitest";

describe("DynamicButtonGroup", () => {
  it("renders multiple buttons with individual props and triggers onClick", () => {
    const onClickOne = vi.fn();
    const onClickTwo = vi.fn();

    render(
      <ButtonGroup
        buttons={[
          {
            label: "First",
            onClick: onClickOne,
            color: "primary",
            variant: "solid",
            children: "First",
          },
          {
            label: "Second",
            onClick: onClickTwo,
            color: "secondary",
            variant: "ghost",
            children: "Second",
          },
        ]}
      />
    );

    const firstButton = screen.getByRole("button", { name: /first/i });
    const secondButton = screen.getByRole("button", { name: /second/i });

    expect(firstButton).toBeInTheDocument();
    expect(secondButton).toBeInTheDocument();

    fireEvent.click(firstButton);
    expect(onClickOne).toHaveBeenCalledTimes(1);

    fireEvent.click(secondButton);
    expect(onClickTwo).toHaveBeenCalledTimes(1);
  });
});
