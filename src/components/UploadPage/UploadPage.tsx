import UploadWidget from "./UploadWidget";
import { useCallback, useEffect } from "react";

export default function UploadPage() {
  const fetchImages = useCallback(async () => {
    try {
      const res = await fetch("/api/list-uploads");
      await res.json();
    } catch (err) {
      console.error(err);
    }
  }, []);

  useEffect(() => {
    fetchImages();
  }, [fetchImages]);

  return (
    <div>
      <h2 className="font-bold mb-4">Upload Your Images</h2>
      <UploadWidget onUploadSuccess={fetchImages} />
    </div>
  );
}
