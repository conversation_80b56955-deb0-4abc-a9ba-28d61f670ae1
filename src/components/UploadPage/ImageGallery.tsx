import Image from "next/image";
import { PhotoProvider, PhotoView } from 'react-photo-view';
import 'react-photo-view/dist/react-photo-view.css';

interface CloudinaryImage {
  public_id: string;
  secure_url: string;
}

interface Props {
  images: CloudinaryImage[];
  loading: boolean;
}

export default function ImageGallery({ images, loading }: Props) {
  if (loading) return <p>Loading images...</p>;
  if (images.length === 0) return <p>No images found.</p>;

  return (
    <PhotoProvider>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
        {images.map(img => (
          <div key={img.public_id}>
            <h3 className="text-sm font-semibold mb-2">{img.public_id}</h3>
            <PhotoView src={img.secure_url}>
              <div className="cursor-pointer">
                <Image
                  src={img.secure_url}
                  alt={img.public_id}
                  width={500}
                  height={500}
                  className="w-full h-auto rounded-md shadow-md hover:opacity-90 transition-opacity"
                />
              </div>
            </PhotoView>
          </div>
        ))}
      </div>
    </PhotoProvider>
  );
}
