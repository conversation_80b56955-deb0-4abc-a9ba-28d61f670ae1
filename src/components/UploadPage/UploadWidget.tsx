"use client";

import { useState } from "react";
import <PERSON>rip<PERSON> from "next/script";
import <PERSON><PERSON> from "../Button/Button";

declare global {
  interface Window {
    cloudinary: {
      createUploadWidget: (
        options: Record<string, unknown>,
        callback: (error: unknown, result: unknown) => void
      ) => { open: () => void };
    };
  }
}

interface UploadWidgetProps {
  onUploadSuccess: () => void;
}

export default function UploadWidget({ onUploadSuccess }: UploadWidgetProps) {
  const [scriptLoaded, setScriptLoaded] = useState(false);

  const handleUploadClick = async () => {
    if (!window.cloudinary || !scriptLoaded) {
      console.error("Cloudinary script not loaded");
      return;
    }

    try {
      const widget = window.cloudinary.createUploadWidget(
        {
          cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
          uploadPreset: "ms-unsigned-uploads",
          apiKey: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
          sources: ["local", "url", "camera"],
        },
        (error, result) => {
          if (error) {
            console.error("Upload error:", error);
          } else if (
            result &&
            typeof result === "object" &&
            "event" in result &&
            (result as { event: string }).event === "success"
          ) {
            onUploadSuccess();
          }
        }
      );

      widget.open();
    } catch (err) {
      console.error("Upload widget init failed:", err);
    }
  };

  return (
    <>
      <Script
        src="https://widget.cloudinary.com/v2.0/global/all.js"
        strategy="afterInteractive"
        onLoad={() => setScriptLoaded(true)}
      />
      <Button
        onClick={handleUploadClick}
        variant="solid"
        color="primary"
        disabled={!scriptLoaded}
      >
        Upload Image
      </Button>
    </>
  );
}
