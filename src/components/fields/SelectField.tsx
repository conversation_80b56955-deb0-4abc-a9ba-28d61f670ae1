import React from "react";
import { Controller } from "react-hook-form";
import { Select, SelectItem, SelectProps } from "@heroui/react";
import { FieldControlProps, SelectOption } from "./types";

// Base SelectField props - compose HeroUI props with our base props
export interface SelectFieldProps extends Omit<SelectProps, "children"> {
  // Override conflicting props from BaseFieldProps
  label?: string;
  placeholder?: string;
  description?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  containerClassName?: string;
  options: SelectOption[];
  allowEmpty?: boolean;
}

// Base SelectField component (no form integration)
export const SelectField: React.FC<SelectFieldProps> = ({
  label,
  error,
  description,
  required,
  disabled,
  options,
  allowEmpty = true,
  containerClassName = "",
  className = "",
  ...selectProps
}) => {
  return (
    <div className={`mb-4 ${containerClassName}`}>
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <Select
        {...selectProps}
        isRequired={required}
        isDisabled={disabled}
        errorMessage={error}
        isInvalid={!!error}
        description={description}
        className={className}
        disallowEmptySelection={!allowEmpty && required}
      >
        {options.map(option => (
          <SelectItem key={option.value}>{option.label}</SelectItem>
        ))}
      </Select>
    </div>
  );
};

// Controller-wrapped version for use with react-hook-form
export interface SelectFieldControlProps extends FieldControlProps {
  options: SelectOption[];
  allowEmpty?: boolean;
}

export const SelectFieldControl: React.FC<SelectFieldControlProps> = ({
  name,
  control,
  rules,
  ...fieldProps
}) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange, onBlur, value },
        fieldState: { error },
      }) => (
        <SelectField
          {...fieldProps}
          selectedKeys={value ? [value] : []}
          onSelectionChange={keys => {
            const selectedKey = Array.from(keys)[0];
            onChange(selectedKey);
          }}
          onBlur={onBlur}
          error={error?.message}
        />
      )}
    />
  );
};
