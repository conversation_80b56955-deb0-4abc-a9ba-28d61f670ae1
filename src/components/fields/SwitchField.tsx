import React from "react";
import { Controller } from "react-hook-form";
import { Switch, SwitchProps } from "@heroui/react";
import { FieldControlProps } from "./types";

// Base SwitchField props - compose HeroUI props with our base props
export interface SwitchFieldProps extends SwitchProps {
  // Override conflicting props from BaseFieldProps
  label?: string;
  placeholder?: string;
  description?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  containerClassName?: string;
}

// Base SwitchField component (no form integration)
export const SwitchField: React.FC<SwitchFieldProps> = ({
  label,
  error,
  description,
  required,
  disabled,
  containerClassName = "",
  className = "",
  ...switchProps
}) => {
  return (
    <div className={`mb-4 ${containerClassName}`}>
      <Switch {...switchProps} isDisabled={disabled} className={className}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Switch>
      {description && (
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      )}
      {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
    </div>
  );
};

// Controller-wrapped version for use with react-hook-form
export const SwitchFieldControl: React.FC<FieldControlProps> = ({
  name,
  control,
  rules,
  ...fieldProps
}) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange, onBlur, value },
        fieldState: { error },
      }) => (
        <SwitchField
          {...fieldProps}
          isSelected={!!value}
          onValueChange={onChange}
          onBlur={onBlur}
          error={error?.message}
        />
      )}
    />
  );
};
