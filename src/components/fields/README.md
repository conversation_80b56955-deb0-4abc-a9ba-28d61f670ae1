# Field Components

This directory contains reusable field components for forms, following Option 2 architecture where each file exports both a base component and a Controller-wrapped version.

## Structure

Each field component file exports:

- **Base Component** (e.g., `TextField`) - Pure UI component for standalone use
- **Control Component** (e.g., `TextFieldControl`) - Wrapped with react-hook-form Controller

## Available Fields

### TextField

- Types: text, email, password, number
- Exports: `TextField`, `TextFieldControl`

### TextareaField

- Multiline text input
- Exports: `TextareaField`, `TextareaFieldControl`

### SelectField

- Dropdown selection
- Exports: `SelectField`, `SelectFieldControl`

### SwitchField

- Toggle/checkbox functionality
- Exports: `SwitchField`, `SwitchFieldControl`

### FileField

- File upload with Firebase integration
- Exports: `FileField`, `FileFieldControl`

## Usage Examples

### Standalone (without form)

```tsx
import { TextField, SelectField } from '@/components/fields';

<TextField
  label="Search"
  placeholder="Type to search..."
  onChange={(e) => handleSearch(e.target.value)}
/>

<SelectField
  label="Filter"
  options={[
    { value: 'all', label: 'All' },
    { value: 'active', label: 'Active' }
  ]}
  onSelectionChange={(keys) => handleFilter(Array.from(keys)[0])}
/>
```

### With React Hook Form

```tsx
import { useForm } from "react-hook-form";
import { TextFieldControl, SelectFieldControl } from "@/components/fields";

const { control, handleSubmit } = useForm();

<form onSubmit={handleSubmit(onSubmit)}>
  <TextFieldControl
    name="username"
    control={control}
    label="Username"
    required
    rules={{ required: "Username is required" }}
  />

  <SelectFieldControl
    name="role"
    control={control}
    label="Role"
    options={[
      { value: "ADMIN", label: "Admin" },
      { value: "USER", label: "User" },
    ]}
  />
</form>;
```

### Dynamic Forms

```tsx
import { renderFields } from "@/components/forms";

const fields: FieldConfig[] = [
  { name: "email", type: "email", label: "Email", required: true },
  { name: "password", type: "password", label: "Password", required: true },
];

<form>{renderFields({ fields, control })}</form>;
```

## Type Safety

All components are fully typed with TypeScript. The base props are defined in `types.ts` and each component extends the appropriate HeroUI component props.

## Styling

Components use consistent styling with:

- Indigo-500 labels
- Border-[#B2B2EF] inputs
- Consistent spacing and sizing
- Error states with red-500 color

## Migration from DynamicFormBuilder

See `MIGRATION_GUIDE.md` for detailed migration instructions.
