import React from "react";
import { Controller } from "react-hook-form";
import {
  Textarea as HeroTextarea,
  TextAreaProps as HeroTextAreaProps,
} from "@heroui/react";
import { FieldControlProps } from "./types";

// Base TextareaField props - compose HeroUI props with our base props
export interface TextareaFieldProps extends HeroTextAreaProps {
  // Override conflicting props from BaseFieldProps
  label?: string;
  placeholder?: string;
  description?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  containerClassName?: string;
}

// Base TextareaField component (no form integration)
export const TextareaField: React.FC<TextareaFieldProps> = ({
  label,
  error,
  description,
  required,
  disabled,
  containerClassName = "",
  className = "",
  ...textareaProps
}) => {
  return (
    <div className={`mb-4 ${containerClassName}`}>
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <HeroTextarea
        {...textareaProps}
        isRequired={required}
        isDisabled={disabled}
        errorMessage={error}
        isInvalid={!!error}
        description={description}
        classNames={{
          base: [
            "text-xs w-full border-2 border-[#B2B2EF] rounded",
            "focus:ring-2 focus:ring-indigo-300 shadow-sm",
            className,
          ].join(" "),
          inputWrapper: "bg-transparent",
        }}
      />
    </div>
  );
};

// Controller-wrapped version for use with react-hook-form
export interface TextareaFieldControlProps extends FieldControlProps {
  rows?: number;
}

export const TextareaFieldControl: React.FC<TextareaFieldControlProps> = ({
  name,
  control,
  rules,
  ...fieldProps
}) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange, onBlur, value, ref },
        fieldState: { error },
      }) => (
        <TextareaField
          {...fieldProps}
          value={value ?? ""}
          onChange={onChange}
          onBlur={onBlur}
          ref={ref}
          error={error?.message}
        />
      )}
    />
  );
};
