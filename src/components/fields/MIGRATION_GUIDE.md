# Migration Guide: From DynamicFormBuilder to Field Components

## Overview

This guide helps you migrate from the monolithic `DynamicFormBuilder` to the new modular field components.

## Key Benefits

1. **Modularity**: Use individual field components when you don't need a full form
2. **Flexibility**: Mix and match fields with custom layouts
3. **Type Safety**: Better TypeScript support with focused interfaces
4. **Reusability**: Use base components without form integration
5. **Simplicity**: Each component has a single responsibility

## Component Structure

Each field type exports two components:

- **Base Component** (e.g., `TextField`): Pure UI component without form integration
- **Control Component** (e.g., `TextFieldControl`): Wrapped with react-hook-form Controller

## Migration Examples

### Example 1: Simple Form

**Before (DynamicFormBuilder):**
```tsx
const fields: FieldConfig[] = [
  {
    name: 'username',
    type: 'text',
    label: 'Username',
    required: true,
  },
  {
    name: 'email',
    type: 'email',
    label: 'Email',
    required: true,
  },
];

<DynamicFormBuilder
  fields={fields}
  onSubmit={handleSubmit}
/>
```

**After (Individual Fields):**
```tsx
const { control, handleSubmit } = useForm();

<form onSubmit={handleSubmit(onSubmit)}>
  <TextFieldControl
    name="username"
    control={control}
    label="Username"
    required
    rules={{ required: 'Username is required' }}
  />
  
  <TextFieldControl
    name="email"
    control={control}
    type="email"
    label="Email"
    required
    rules={{ required: 'Email is required' }}
  />
  
  <Button type="submit">Submit</Button>
</form>
```

### Example 2: Dynamic Form with Conditional Fields

**Using renderFields utility:**
```tsx
const { control, watch } = useForm();
const watchedValues = watch();

const fields: FieldConfig[] = [
  {
    name: 'hasAccount',
    type: 'switch',
    label: 'I have an account',
  },
  {
    name: 'accountNumber',
    type: 'text',
    label: 'Account Number',
    renderCondition: (values) => values.hasAccount,
  },
];

<form>
  <div className="space-y-4">
    {renderFields({ fields, control, watchedValues })}
  </div>
</form>
```

### Example 3: Using Base Components (No Form)

```tsx
// Search/filter UI without form submission
<div className="flex gap-4">
  <TextField
    label="Search"
    placeholder="Type to search..."
    onChange={(e) => handleSearch(e.target.value)}
  />
  
  <SelectField
    label="Status"
    options={statusOptions}
    onSelectionChange={(keys) => handleFilter(Array.from(keys)[0])}
  />
</div>
```

## Field Type Mapping

| DynamicFormBuilder Type | New Component |
|------------------------|---------------|
| `text`, `email`, `password`, `number` | `TextFieldControl` |
| `textarea` | `TextareaFieldControl` |
| `select` | `SelectFieldControl` |
| `switch`, `checkbox` | `SwitchFieldControl` |
| `file` | `FileFieldControl` |

## Advanced Features

### Custom Validation

```tsx
<TextFieldControl
  name="username"
  control={control}
  rules={{
    required: 'Username is required',
    minLength: { value: 3, message: 'Must be at least 3 characters' },
    pattern: { value: /^[a-zA-Z0-9]+$/, message: 'Alphanumeric only' }
  }}
/>
```

### Grid Layouts

```tsx
<div className="grid grid-cols-12 gap-4">
  <div className="col-span-6">
    <TextFieldControl name="firstName" control={control} />
  </div>
  <div className="col-span-6">
    <TextFieldControl name="lastName" control={control} />
  </div>
  <div className="col-span-12">
    <TextareaFieldControl name="bio" control={control} />
  </div>
</div>
```

### When to Keep Using DynamicFormBuilder

You might want to keep using DynamicFormBuilder when:

1. You have truly dynamic forms loaded from a database/API
2. You need complex section-based layouts
3. You want automatic form generation from schemas

## Best Practices

1. **Use Control components** when working with react-hook-form
2. **Use Base components** for non-form UI (filters, search, etc.)
3. **Leverage renderFields** for semi-dynamic forms
4. **Create custom field components** following the same pattern for consistency

## Need Help?

- Check `example-usage.tsx` for complete examples
- All components have TypeScript intellisense
- Base and Control components share the same props (plus form-specific ones)