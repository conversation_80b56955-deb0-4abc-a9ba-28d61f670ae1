import React from "react";
import { Controller } from "react-hook-form";
import {
  Input as HeroInput,
  InputProps as HeroInputProps,
} from "@heroui/react";
import { FieldControlProps } from "./types";

// Base TextField props - compose HeroUI props with our base props
export interface TextFieldProps extends Omit<HeroInputProps, "type"> {
  // Override conflicting props from BaseFieldProps
  label?: string;
  placeholder?: string;
  description?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  containerClassName?: string;
  type?: "text" | "email" | "password" | "number";
}

// Base TextField component (no form integration)
export const TextField: React.FC<TextFieldProps> = ({
  label,
  error,
  description,
  required,
  disabled,
  containerClassName = "",
  className = "",
  ...inputProps
}) => {
  return (
    <div className={`mb-4 ${containerClassName}`}>
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <HeroInput
        {...inputProps}
        isRequired={required}
        isDisabled={disabled}
        errorMessage={error}
        isInvalid={!!error}
        description={description}
        classNames={{
          base: [
            "text-xs w-full border-2 border-[#B2B2EF] rounded",
            "focus:ring-2 focus:ring-indigo-300 shadow-sm",
            className,
          ].join(" "),
          inputWrapper: "bg-transparent",
        }}
      />
    </div>
  );
};

// Controller-wrapped version for use with react-hook-form
export interface TextFieldControlProps extends FieldControlProps {
  type?: "text" | "email" | "password" | "number";
}

export const TextFieldControl: React.FC<TextFieldControlProps> = ({
  name,
  control,
  rules,
  ...fieldProps
}) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange, onBlur, value, ref },
        fieldState: { error },
      }) => (
        <TextField
          {...fieldProps}
          value={value ?? ""}
          onChange={onChange}
          onBlur={onBlur}
          ref={ref}
          error={error?.message}
        />
      )}
    />
  );
};
