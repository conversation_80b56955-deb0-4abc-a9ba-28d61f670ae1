import { Control, RegisterOptions, FieldValues } from "react-hook-form";

// Base props that all field components share
export interface BaseFieldProps {
  label?: string;
  placeholder?: string;
  description?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  containerClassName?: string;
}

// Props for Controller-wrapped versions
export interface FieldControlProps extends BaseFieldProps {
  name: string;
  control: Control<FieldValues>;
  rules?: RegisterOptions;
}

// Field configuration for dynamic rendering
export type FieldType =
  | "text"
  | "email"
  | "number"
  | "password"
  | "textarea"
  | "select"
  | "switch"
  | "checkbox"
  | "radio"
  | "file"
  | "hidden"
  | "custom";

export interface SelectOption {
  value: string;
  label: string;
  description?: string;
}

export interface FieldConfig extends BaseFieldProps {
  name: string;
  type: FieldType;

  // Type-specific options
  options?: SelectOption[]; // for select, radio
  accept?: string; // for file inputs
  rows?: number; // for textarea
  defaultValue?: unknown;

  // File upload specific
  storagePath?: string;
  useUUID?: boolean;

  // Layout
  gridSpan?: "full" | "half" | "third";

  // Advanced
  renderCondition?: (values: Record<string, unknown>) => boolean;
  render?: (props: Record<string, unknown>) => React.ReactNode;
}
