import React from "react";
import { Controller } from "react-hook-form";
import SimpleFirebaseUploader from "@/components/common/SimpleFirebaseUploader/SimpleFirebaseUploader";
import { BaseFieldProps, FieldControlProps } from "./types";

// Base FileField props
export interface FileFieldProps extends BaseFieldProps {
  accept?: string;
  storagePath?: string;
  useUUID?: boolean;
  value?: string;
  onChange?: (
    result: { url: string; path: string; fileName: string } | null
  ) => void;
}

// Base FileField component (no form integration)
export const FileField: React.FC<FileFieldProps> = ({
  label,
  error,
  description,
  required,
  accept,
  storagePath = "uploads",
  useUUID = true,
  onChange,
  containerClassName = "",
  className = "",
  disabled,
}) => {
  return (
    <div className={`mb-4 ${containerClassName}`}>
      {label && (
        <label className="text-xs text-indigo-500 font-semibold mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div className="relative">
        <SimpleFirebaseUploader
          onUploadComplete={onChange || (() => {})}
          storagePath={storagePath}
          accept={accept}
          useUUID={useUUID}
          className={className}
        />
        {disabled && (
          <div className="absolute inset-0 bg-gray-100 bg-opacity-50 cursor-not-allowed" />
        )}
      </div>

      {description && (
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      )}
      {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
    </div>
  );
};

// Controller-wrapped version for use with react-hook-form
export interface FileFieldControlProps extends FieldControlProps {
  accept?: string;
  storagePath?: string;
  useUUID?: boolean;
}

export const FileFieldControl: React.FC<FileFieldControlProps> = ({
  name,
  control,
  rules,
  ...fieldProps
}) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <FileField
          {...fieldProps}
          value={value}
          onChange={result => onChange(result?.url || "")}
          error={error?.message}
        />
      )}
    />
  );
};
