"use client";
import { useState } from "react";
import CharacterCreation from "../CharacterCreate/CharacterCreate";
import Button from "../Button/Button";

export default function Sidebar() {
  const [showCharacterCreation, setShowCharacterCreation] = useState(false);

  return (
    <>
      <aside className="fixed top-0 left-0 h-full w-64 bg-white shadow-lg p-4 border-r border-gray-200 flex flex-col gap-4 z-50">
        <h2 className="text-lg font-semibold">Sidebar</h2>
        <Button
          onClick={() => setShowCharacterCreation(prevState => !prevState)}
          variant="solid"
          color="primary"
        >
          Characters
        </Button>
      </aside>
      {showCharacterCreation && (
        <CharacterCreation
          open={showCharacterCreation}
          onClose={() => setShowCharacterCreation(false)}
        />
      )}
    </>
  );
}
