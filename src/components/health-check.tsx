"use client";

import { useHealthQuery, HealthQuery } from "@/graphql/generated/graphql";

export function HealthCheck() {
  const { data, loading, error } = useHealthQuery({
    pollInterval: 5000,
  });

  if (loading)
    return (
      <div className="text-sm text-gray-600">Loading health status...</div>
    );
  if (error)
    return <div className="text-sm text-red-600">Error: {error.message}</div>;

  const healthFields = extractHealthFields(data?.health);

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-2">API Health Status</h3>
      {healthFields.length > 0 && (
        <div className="space-y-2 text-sm">
          {healthFields.map((field, index) => (
            <div key={index}>
              <span className="font-medium">{field.name}:</span> {field.value}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function extractHealthFields(health: HealthQuery["health"] | undefined) {
  if (!health) return [];

  return [
    {
      name: "Status",
      value: health?.status || "N/A",
    },
    {
      name: "Service",
      value: health?.service || "N/A",
    },
    // TODO: Add these fields when they are available in the GraphQL schema
    // {
    //   name: 'Uptime',
    //   value: health?.uptime
    //     ? `${Math.floor(health.uptime / 60)} minutes`
    //     : 'N/A',
    // },
    // {
    //   name: 'Memory Usage',
    //   value:
    //     health?.memoryUsage !== undefined
    //       ? `${health.memoryUsage.toFixed(2)}%`
    //       : 'N/A',
    // },
  ];
}
