"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
} from "react";
import { useAuth } from "@clerk/nextjs";
import {
  type SyncUserMutation,
  useSyncUserMutation,
} from "@/graphql/generated/graphql";

// Derive Project type from GraphQL generated types
type Project = SyncUserMutation["syncUser"]["recentProjects"][0];

interface UserState {
  recentProjects: Project[];
  defaultProjectId: string | null;
  isLoading: boolean;
  error: Error | null;
}

type UserAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: Error | null }
  | {
      type: "SET_PROJECTS";
      payload: { projects: Project[]; defaultProjectId?: string };
    }
  | { type: "SET_DEFAULT_PROJECT"; payload: string }
  | { type: "RESET" };

const initialState: UserState = {
  recentProjects: [],
  defaultProjectId: null,
  isLoading: true,
  error: null,
};

function userReducer(state: UserState, action: UserAction): UserState {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };
    case "SET_ERROR":
      return { ...state, error: action.payload };
    case "SET_PROJECTS":
      return {
        ...state,
        recentProjects: action.payload.projects,
        defaultProjectId:
          action.payload.defaultProjectId ||
          state.defaultProjectId ||
          action.payload.projects[0]?.id ||
          null,
      };
    case "SET_DEFAULT_PROJECT":
      return { ...state, defaultProjectId: action.payload };
    case "RESET":
      return initialState;
    default:
      return state;
  }
}

interface UserContextType {
  recentProjects: Project[];
  defaultProjectId: string | null;
  isLoading: boolean;
  error: Error | null;
  syncUser: () => Promise<void>;
  setDefaultProjectId: (projectId: string) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { isLoaded, isSignedIn } = useAuth();
  const [state, dispatch] = useReducer(userReducer, initialState);
  const [syncUserMutation] = useSyncUserMutation();

  const syncUser = useCallback(async () => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      const { data } = await syncUserMutation();

      if (data?.syncUser?.recentProjects) {
        const projects = data.syncUser.recentProjects.map(p => ({
          id: p.id,
          name: p.name,
        }));

        dispatch({
          type: "SET_PROJECTS",
          payload: {
            projects,
            defaultProjectId: projects?.[0]?.id,
          },
        });
      }
    } catch (err) {
      dispatch({ type: "SET_ERROR", payload: err as Error });
      console.error("Failed to sync user:", err);
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  }, [syncUserMutation]);

  useEffect(() => {
    if (!isLoaded) return;

    if (isSignedIn) {
      syncUser();
    } else {
      dispatch({ type: "RESET" });
    }
  }, [isLoaded, isSignedIn, syncUser]);

  const setDefaultProjectId = useCallback((projectId: string) => {
    dispatch({ type: "SET_DEFAULT_PROJECT", payload: projectId });
  }, []);

  return (
    <UserContext.Provider
      value={{
        recentProjects: state.recentProjects,
        defaultProjectId: state.defaultProjectId,
        isLoading: state.isLoading,
        error: state.error,
        syncUser,
        setDefaultProjectId,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
