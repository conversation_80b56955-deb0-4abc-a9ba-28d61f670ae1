"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useAuth, useUser } from "@clerk/nextjs";
import {
  onAuthStateChanged,
  signInWithCustomToken,
  signOut,
  User,
} from "firebase/auth";
import { auth } from "@/lib/firebase";

interface FirebaseContextType {
  firebaseUser: User | null;
  loading: boolean;
  error: Error | null;
}

const FirebaseContext = createContext<FirebaseContextType>({
  firebaseUser: null,
  loading: true,
  error: null,
});

export const useFirebase = () => {
  const context = useContext(FirebaseContext);
  if (!context) {
    throw new Error("useFirebase must be used within a FirebaseProvider");
  }
  return context;
};

interface FirebaseProviderProps {
  children: React.ReactNode;
}

export function FirebaseProvider({ children }: FirebaseProviderProps) {
  const { user, isLoaded } = useUser();
  const { getToken } = useAuth();
  const [firebaseUser, setFirebaseUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Listen to Firebase auth state changes
    const unsubscribe = onAuthStateChanged(auth, fbUser => {
      setFirebaseUser(fbUser);
      if (!user) {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [user]);

  useEffect(() => {
    const syncAuth = async () => {
      if (!isLoaded) return;

      try {
        setError(null);

        if (user) {
          // Get custom token from Clerk
          const firebaseToken = await getToken({
            template: "integration_firebase",
          });

          if (firebaseToken) {
            await signInWithCustomToken(auth, firebaseToken);
          } else {
            throw new Error("Failed to get Firebase token from Clerk");
          }
        } else if (firebaseUser) {
          // User signed out from Clerk, sign out from Firebase too
          await signOut(auth);
          setFirebaseUser(null);
        }
      } catch (err) {
        console.error("Error syncing Firebase auth:", err);
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    syncAuth();
  }, [user, isLoaded, getToken, firebaseUser]);

  return (
    <FirebaseContext.Provider value={{ firebaseUser, loading, error }}>
      {children}
    </FirebaseContext.Provider>
  );
}
