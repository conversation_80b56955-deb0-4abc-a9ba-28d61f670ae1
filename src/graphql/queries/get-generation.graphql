query GetGeneration($generationId: String!) {
  generation(id: $generationId) {
    context {
      characterId
      characterVariantId
      organizationId
      parentAssetId
      projectId
      sessionId
      storyboardId
      userId
      workspaceId
    }
    createdAt
    error
    id
    model
    params
    progress
    provider
    result {
      assets {
        url
        type
      }
      metadata
    }
    status
    tool
    updatedAt
  }
}
