import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never;
    };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  DateTime: { input: any; output: any };
  JSON: { input: any; output: any };
  UUID: { input: any; output: any };
};

export type Asset = {
  __typename?: "Asset";
  _id: Scalars["ID"]["output"];
  context: GenerationContext;
  createdAt: Scalars["DateTime"]["output"];
  generationId?: Maybe<Scalars["String"]["output"]>;
  generationParams?: Maybe<GenerationParams>;
  id: Scalars["ID"]["output"];
  metadata?: Maybe<AssetMetadata>;
  originalUrl?: Maybe<Scalars["String"]["output"]>;
  source: AssetSource;
  status: AssetStatus;
  sync?: Maybe<AssetSyncInfoType>;
  type: Scalars["String"]["output"];
  updatedAt: Scalars["DateTime"]["output"];
  url: Scalars["String"]["output"];
};

export type AssetMetadata = {
  __typename?: "AssetMetadata";
  dimensions?: Maybe<Array<Scalars["Float"]["output"]>>;
  duration?: Maybe<Scalars["Float"]["output"]>;
  fileSize?: Maybe<Scalars["Float"]["output"]>;
  mimeType?: Maybe<Scalars["String"]["output"]>;
};

/** The source of an asset */
export enum AssetSource {
  Generated = "GENERATED",
  Imported = "IMPORTED",
  Uploaded = "UPLOADED",
}

/** The status of an asset */
export enum AssetStatus {
  Archived = "ARCHIVED",
  Failed = "FAILED",
  Pending = "PENDING",
  Processing = "PROCESSING",
  Ready = "READY",
  Synced = "SYNCED",
  Syncing = "SYNCING",
}

export type AssetSyncInfoType = {
  __typename?: "AssetSyncInfoType";
  cdnUrl?: Maybe<Scalars["String"]["output"]>;
  error?: Maybe<Scalars["String"]["output"]>;
  provider?: Maybe<Scalars["String"]["output"]>;
  status: Scalars["String"]["output"];
  syncedAt?: Maybe<Scalars["DateTime"]["output"]>;
};

export type Character = {
  __typename?: "Character";
  _id: Scalars["ID"]["output"];
  context?: Maybe<CharacterContext>;
  createdAt: Scalars["DateTime"]["output"];
  createdBy: Scalars["String"]["output"];
  deletedAt?: Maybe<Scalars["DateTime"]["output"]>;
  image?: Maybe<CharacterImage>;
  metadata?: Maybe<Scalars["JSON"]["output"]>;
  name: Scalars["String"]["output"];
  updatedAt: Scalars["DateTime"]["output"];
};

export type CharacterContext = {
  __typename?: "CharacterContext";
  additional?: Maybe<Scalars["JSON"]["output"]>;
  projectId?: Maybe<Scalars["String"]["output"]>;
  sessionId?: Maybe<Scalars["String"]["output"]>;
  storyboardId?: Maybe<Scalars["String"]["output"]>;
  workspaceId?: Maybe<Scalars["String"]["output"]>;
};

export type CharacterContextInput = {
  additional?: InputMaybe<Scalars["JSON"]["input"]>;
  projectId?: InputMaybe<Scalars["String"]["input"]>;
  sessionId?: InputMaybe<Scalars["String"]["input"]>;
  storyboardId?: InputMaybe<Scalars["String"]["input"]>;
};

export type CharacterImage = {
  __typename?: "CharacterImage";
  url: Scalars["String"]["output"];
};

export type CreateCharacterInput = {
  context?: InputMaybe<CharacterContextInput>;
  imageUrl?: InputMaybe<Scalars["String"]["input"]>;
  metadata?: InputMaybe<Scalars["JSON"]["input"]>;
  name: Scalars["String"]["input"];
};

export type FeatureFlag = {
  __typename?: "FeatureFlag";
  enabled: Scalars["Boolean"]["output"];
  key: Scalars["String"]["output"];
  metadata?: Maybe<Scalars["JSON"]["output"]>;
};

export type GenerateResponse = {
  __typename?: "GenerateResponse";
  id: Scalars["UUID"]["output"];
  progress: Scalars["Int"]["output"];
  status: Scalars["String"]["output"];
};

export type Generation = {
  __typename?: "Generation";
  billing?: Maybe<GenerationBilling>;
  completedAt?: Maybe<Scalars["DateTime"]["output"]>;
  context: GenerationContext;
  createdAt: Scalars["DateTime"]["output"];
  error?: Maybe<Scalars["String"]["output"]>;
  externalRequestId?: Maybe<Scalars["String"]["output"]>;
  id: Scalars["UUID"]["output"];
  model: Scalars["String"]["output"];
  params: Scalars["JSON"]["output"];
  progress: Scalars["Int"]["output"];
  provider?: Maybe<Scalars["String"]["output"]>;
  result?: Maybe<GenerationResult>;
  startedAt?: Maybe<Scalars["DateTime"]["output"]>;
  status: GenerationStatus;
  tool: Scalars["String"]["output"];
  updatedAt: Scalars["DateTime"]["output"];
  webhookUrl?: Maybe<Scalars["String"]["output"]>;
};

export type GenerationAsset = {
  __typename?: "GenerationAsset";
  type: Scalars["String"]["output"];
  url: Scalars["String"]["output"];
};

export type GenerationBilling = {
  __typename?: "GenerationBilling";
  creditsUsed?: Maybe<Scalars["Float"]["output"]>;
  plan?: Maybe<Scalars["String"]["output"]>;
};

export type GenerationContext = {
  __typename?: "GenerationContext";
  characterId?: Maybe<Scalars["String"]["output"]>;
  characterVariantId?: Maybe<Scalars["String"]["output"]>;
  organizationId?: Maybe<Scalars["String"]["output"]>;
  parentAssetId?: Maybe<Scalars["String"]["output"]>;
  projectId: Scalars["String"]["output"];
  sessionId?: Maybe<Scalars["String"]["output"]>;
  storyboardId?: Maybe<Scalars["String"]["output"]>;
  userId: Scalars["String"]["output"];
  workspaceId: Scalars["String"]["output"];
};

export type GenerationContextInput = {
  characterId?: InputMaybe<Scalars["String"]["input"]>;
  characterVariantId?: InputMaybe<Scalars["String"]["input"]>;
  parentAssetId?: InputMaybe<Scalars["String"]["input"]>;
  projectId: Scalars["String"]["input"];
  sessionId?: InputMaybe<Scalars["String"]["input"]>;
  storyboardId?: InputMaybe<Scalars["String"]["input"]>;
};

export type GenerationParams = {
  __typename?: "GenerationParams";
  model: Scalars["String"]["output"];
  /** JSON stringified params */
  params: Scalars["String"]["output"];
  tool: Scalars["String"]["output"];
};

export type GenerationResult = {
  __typename?: "GenerationResult";
  assets: Array<GenerationAsset>;
  metadata?: Maybe<Scalars["JSON"]["output"]>;
};

export enum GenerationStatus {
  Completed = "COMPLETED",
  Failed = "FAILED",
  Pending = "PENDING",
  Processing = "PROCESSING",
}

export type HealthResponse = {
  __typename?: "HealthResponse";
  /** Health check ID */
  id: Scalars["UUID"]["output"];
  /** Service name */
  service: Scalars["String"]["output"];
  /** Service status */
  status: Scalars["String"]["output"];
  /** Timestamp of the health check */
  timestamp: Scalars["DateTime"]["output"];
  /** Service version */
  version: Scalars["String"]["output"];
};

export type ImportAssetInput = {
  characterId?: InputMaybe<Scalars["String"]["input"]>;
  characterVariantId?: InputMaybe<Scalars["String"]["input"]>;
  description?: InputMaybe<Scalars["String"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
  storyboardId?: InputMaybe<Scalars["String"]["input"]>;
  type: Scalars["String"]["input"];
  url: Scalars["String"]["input"];
};

export type Mutation = {
  __typename?: "Mutation";
  archiveAsset: Asset;
  archiveProject: Project;
  createCharacter: Character;
  createProject: Project;
  deleteCharacter: Character;
  generate: GenerateResponse;
  importAsset: Asset;
  replayAsset: GenerateResponse;
  setDefaultWorkspace: User;
  syncAsset: Asset;
  syncUser: UserProfile;
  updateAssetStatus: Asset;
  updateCharacter: Character;
  updateMe: User;
  updateProject: Project;
  updateWorkspace: Workspace;
};

export type MutationArchiveAssetArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationArchiveProjectArgs = {
  id: Scalars["String"]["input"];
};

export type MutationCreateCharacterArgs = {
  input: CreateCharacterInput;
};

export type MutationCreateProjectArgs = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
};

export type MutationDeleteCharacterArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationGenerateArgs = {
  context?: InputMaybe<GenerationContextInput>;
  params: Scalars["JSON"]["input"];
  tool: Scalars["String"]["input"];
};

export type MutationImportAssetArgs = {
  input: ImportAssetInput;
  projectId: Scalars["ID"]["input"];
};

export type MutationReplayAssetArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationSetDefaultWorkspaceArgs = {
  workspaceId: Scalars["ID"]["input"];
};

export type MutationSyncAssetArgs = {
  id: Scalars["ID"]["input"];
};

export type MutationUpdateAssetStatusArgs = {
  id: Scalars["ID"]["input"];
  status: AssetStatus;
};

export type MutationUpdateCharacterArgs = {
  id: Scalars["ID"]["input"];
  input: UpdateCharacterInput;
};

export type MutationUpdateMeArgs = {
  firstName?: InputMaybe<Scalars["String"]["input"]>;
  lastName?: InputMaybe<Scalars["String"]["input"]>;
  username?: InputMaybe<Scalars["String"]["input"]>;
};

export type MutationUpdateProjectArgs = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  id: Scalars["String"]["input"];
  name?: InputMaybe<Scalars["String"]["input"]>;
  status?: InputMaybe<ProjectStatus>;
};

export type MutationUpdateWorkspaceArgs = {
  name: Scalars["String"]["input"];
};

export type PaginatedAssetResponse = {
  __typename?: "PaginatedAssetResponse";
  items: Array<Asset>;
  pagination: PaginationInfo;
};

export type PaginationInfo = {
  __typename?: "PaginationInfo";
  hasNext: Scalars["Boolean"]["output"];
  hasPrev: Scalars["Boolean"]["output"];
  limit: Scalars["Int"]["output"];
  page: Scalars["Int"]["output"];
  totalItems: Scalars["Int"]["output"];
  totalPages: Scalars["Int"]["output"];
};

export type Project = {
  __typename?: "Project";
  createdAt: Scalars["DateTime"]["output"];
  createdBy: Scalars["String"]["output"];
  deletedAt?: Maybe<Scalars["DateTime"]["output"]>;
  description?: Maybe<Scalars["String"]["output"]>;
  id: Scalars["ID"]["output"];
  isDefault: Scalars["Boolean"]["output"];
  metadata: Scalars["JSON"]["output"];
  name: Scalars["String"]["output"];
  slug: Scalars["String"]["output"];
  status: ProjectStatus;
  updatedAt: Scalars["DateTime"]["output"];
  workspaceId: Scalars["String"]["output"];
};

/** The status of a project */
export enum ProjectStatus {
  Active = "ACTIVE",
  Archived = "ARCHIVED",
  Deleted = "DELETED",
}

export type Query = {
  __typename?: "Query";
  asset: Asset;
  assetsByProject: PaginatedAssetResponse;
  /** Get all available AI generation tools */
  availableTools: Array<ToolInfo>;
  character?: Maybe<Character>;
  characters: Array<Character>;
  currentWorkspace: Workspace;
  generation?: Maybe<Generation>;
  health: HealthResponse;
  myAssets: PaginatedAssetResponse;
  myGenerations: Array<Generation>;
  profile: UserProfile;
  project: Project;
  userProjects: Array<Project>;
  workspace: Workspace;
  workspaceProjects: Array<Project>;
};

export type QueryAssetArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryAssetsByProjectArgs = {
  limit?: Scalars["Int"]["input"];
  page?: Scalars["Int"]["input"];
  projectId: Scalars["ID"]["input"];
  sort?: InputMaybe<Scalars["JSON"]["input"]>;
};

export type QueryCharacterArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryCharactersArgs = {
  projectId?: InputMaybe<Scalars["String"]["input"]>;
  workspaceId?: InputMaybe<Scalars["String"]["input"]>;
};

export type QueryGenerationArgs = {
  id: Scalars["String"]["input"];
};

export type QueryMyAssetsArgs = {
  limit?: Scalars["Int"]["input"];
  page?: Scalars["Int"]["input"];
  sort?: InputMaybe<Scalars["JSON"]["input"]>;
};

export type QueryProjectArgs = {
  id: Scalars["String"]["input"];
};

export type QueryWorkspaceArgs = {
  id: Scalars["String"]["input"];
};

export type Subscription = {
  __typename?: "Subscription";
  generationsByProject: Generation;
};

export type SubscriptionGenerationsByProjectArgs = {
  projectId: Scalars["String"]["input"];
};

/** Category of AI generation tool */
export enum ToolCategory {
  Avatar = "AVATAR",
  Image = "IMAGE",
  Object = "OBJECT",
  Video = "VIDEO",
}

export type ToolInfo = {
  __typename?: "ToolInfo";
  category: ToolCategory;
  description: Scalars["String"]["output"];
  key: Scalars["String"]["output"];
  name: Scalars["String"]["output"];
  pricing: ToolPricing;
  provider: Scalars["String"]["output"];
  /** JSON Schema for tool input validation */
  schema: Scalars["JSON"]["output"];
};

export type ToolPricing = {
  __typename?: "ToolPricing";
  baseCredits: Scalars["Float"]["output"];
  /** Whether pricing has a dynamic multiplier */
  hasDynamicMultiplier?: Maybe<Scalars["Boolean"]["output"]>;
};

export type UpdateCharacterInput = {
  context?: InputMaybe<CharacterContextInput>;
  imageUrl?: InputMaybe<Scalars["String"]["input"]>;
  metadata?: InputMaybe<Scalars["JSON"]["input"]>;
  name?: InputMaybe<Scalars["String"]["input"]>;
};

export type User = {
  __typename?: "User";
  _id: Scalars["ID"]["output"];
  createdAt: Scalars["DateTime"]["output"];
  defaultWorkspaceId?: Maybe<Scalars["String"]["output"]>;
  email: Scalars["String"]["output"];
  firstName: Scalars["String"]["output"];
  isActive: Scalars["Boolean"]["output"];
  lastName: Scalars["String"]["output"];
  lastSignInAt?: Maybe<Scalars["DateTime"]["output"]>;
  role: UserRole;
  updatedAt: Scalars["DateTime"]["output"];
  username?: Maybe<Scalars["String"]["output"]>;
};

export type UserProfile = {
  __typename?: "UserProfile";
  _id: Scalars["ID"]["output"];
  createdAt: Scalars["DateTime"]["output"];
  defaultWorkspace?: Maybe<Workspace>;
  defaultWorkspaceId?: Maybe<Scalars["String"]["output"]>;
  email: Scalars["String"]["output"];
  emailAddresses?: Maybe<Array<Scalars["String"]["output"]>>;
  firstName: Scalars["String"]["output"];
  hasImage: Scalars["Boolean"]["output"];
  imageUrl?: Maybe<Scalars["String"]["output"]>;
  isActive: Scalars["Boolean"]["output"];
  lastName: Scalars["String"]["output"];
  lastSignInAt?: Maybe<Scalars["DateTime"]["output"]>;
  phoneNumber?: Maybe<Scalars["String"]["output"]>;
  recentProjects: Array<Project>;
  role: UserRole;
  twoFactorEnabled: Scalars["Boolean"]["output"];
  updatedAt: Scalars["DateTime"]["output"];
  username?: Maybe<Scalars["String"]["output"]>;
  workspaces: Array<Workspace>;
};

/** The role of a user in the system */
export enum UserRole {
  Admin = "ADMIN",
  User = "USER",
}

export type Workspace = {
  __typename?: "Workspace";
  billingCustomerId?: Maybe<Scalars["String"]["output"]>;
  clerkOrgId?: Maybe<Scalars["String"]["output"]>;
  createdAt: Scalars["DateTime"]["output"];
  createdBy: Scalars["String"]["output"];
  deletedAt?: Maybe<Scalars["DateTime"]["output"]>;
  featureFlags: Array<FeatureFlag>;
  id: Scalars["ID"]["output"];
  members: Array<WorkspaceMember>;
  name: Scalars["String"]["output"];
  ownerId: Scalars["String"]["output"];
  plan: WorkspacePlan;
  slug: Scalars["String"]["output"];
  type: WorkspaceType;
  updatedAt: Scalars["DateTime"]["output"];
};

export type WorkspaceMember = {
  __typename?: "WorkspaceMember";
  invitedBy?: Maybe<Scalars["String"]["output"]>;
  joinedAt: Scalars["DateTime"]["output"];
  role: WorkspaceMemberRole;
  status: WorkspaceMemberStatus;
  userId: Scalars["String"]["output"];
};

/** The role of a workspace member */
export enum WorkspaceMemberRole {
  Admin = "ADMIN",
  Member = "MEMBER",
  Owner = "OWNER",
}

/** The status of a workspace member */
export enum WorkspaceMemberStatus {
  Active = "ACTIVE",
  Invited = "INVITED",
  Pending = "PENDING",
}

/** The billing plan for the workspace */
export enum WorkspacePlan {
  Basic = "BASIC",
  Free = "FREE",
  Pro = "PRO",
  Team = "TEAM",
}

/** The type of workspace */
export enum WorkspaceType {
  Organization = "ORGANIZATION",
  Personal = "PERSONAL",
}

export type CreateCharacterMutationVariables = Exact<{
  input: CreateCharacterInput;
}>;

export type CreateCharacterMutation = {
  __typename?: "Mutation";
  createCharacter: {
    __typename?: "Character";
    _id: string;
    name: string;
    image?: { __typename?: "CharacterImage"; url: string } | null;
  };
};

export type GenerateCharacterMutationVariables = Exact<{
  params: Scalars["JSON"]["input"];
  tool: Scalars["String"]["input"];
  context?: InputMaybe<GenerationContextInput>;
}>;

export type GenerateCharacterMutation = {
  __typename?: "Mutation";
  generate: {
    __typename?: "GenerateResponse";
    id: any;
    progress: number;
    status: string;
  };
};

export type GenerateMutationVariables = Exact<{
  params: Scalars["JSON"]["input"];
  tool: Scalars["String"]["input"];
  context?: InputMaybe<GenerationContextInput>;
}>;

export type GenerateMutation = {
  __typename?: "Mutation";
  generate: {
    __typename?: "GenerateResponse";
    id: any;
    progress: number;
    status: string;
  };
};

export type GenerationByProjectSubscriptionVariables = Exact<{
  projectId: Scalars["String"]["input"];
}>;

export type GenerationByProjectSubscription = {
  __typename?: "Subscription";
  generationsByProject: {
    __typename?: "Generation";
    id: any;
    progress: number;
    result?: {
      __typename?: "GenerationResult";
      assets: Array<{
        __typename?: "GenerationAsset";
        url: string;
        type: string;
      }>;
    } | null;
  };
};

export type GetGenerationsByProjectSubscriptionVariables = Exact<{
  projectId: Scalars["String"]["input"];
}>;

export type GetGenerationsByProjectSubscription = {
  __typename?: "Subscription";
  generationsByProject: {
    __typename?: "Generation";
    id: any;
    params: any;
    model: string;
    progress: number;
    provider?: string | null;
    result?: {
      __typename?: "GenerationResult";
      assets: Array<{
        __typename?: "GenerationAsset";
        type: string;
        url: string;
      }>;
    } | null;
    context: {
      __typename?: "GenerationContext";
      projectId: string;
      sessionId?: string | null;
    };
  };
};

export type AvailableToolsQueryVariables = Exact<{ [key: string]: never }>;

export type AvailableToolsQuery = {
  __typename?: "Query";
  availableTools: Array<{
    __typename?: "ToolInfo";
    category: ToolCategory;
    description: string;
    key: string;
    name: string;
    provider: string;
    schema: any;
    pricing: {
      __typename?: "ToolPricing";
      baseCredits: number;
      hasDynamicMultiplier?: boolean | null;
    };
  }>;
};

export type CharacterQueryVariables = Exact<{
  characterId: Scalars["ID"]["input"];
}>;

export type CharacterQuery = {
  __typename?: "Query";
  character?: {
    __typename?: "Character";
    _id: string;
    metadata?: any | null;
    image?: { __typename?: "CharacterImage"; url: string } | null;
  } | null;
};

export type CharactersQueryVariables = Exact<{ [key: string]: never }>;

export type CharactersQuery = {
  __typename?: "Query";
  characters: Array<{
    __typename?: "Character";
    _id: string;
    name: string;
    image?: { __typename?: "CharacterImage"; url: string } | null;
  }>;
};

export type GetGenerationQueryVariables = Exact<{
  generationId: Scalars["String"]["input"];
}>;

export type GetGenerationQuery = {
  __typename?: "Query";
  generation?: {
    __typename?: "Generation";
    createdAt: any;
    error?: string | null;
    id: any;
    model: string;
    params: any;
    progress: number;
    provider?: string | null;
    status: GenerationStatus;
    tool: string;
    updatedAt: any;
    context: {
      __typename?: "GenerationContext";
      characterId?: string | null;
      characterVariantId?: string | null;
      organizationId?: string | null;
      parentAssetId?: string | null;
      projectId: string;
      sessionId?: string | null;
      storyboardId?: string | null;
      userId: string;
      workspaceId: string;
    };
    result?: {
      __typename?: "GenerationResult";
      metadata?: any | null;
      assets: Array<{
        __typename?: "GenerationAsset";
        url: string;
        type: string;
      }>;
    } | null;
  } | null;
};

export type HealthQueryVariables = Exact<{ [key: string]: never }>;

export type HealthQuery = {
  __typename?: "Query";
  health: {
    __typename?: "HealthResponse";
    id: any;
    service: string;
    status: string;
    timestamp: any;
    version: string;
  };
};

export type ProfileQueryVariables = Exact<{ [key: string]: never }>;

export type ProfileQuery = {
  __typename?: "Query";
  profile: {
    __typename?: "UserProfile";
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    isActive: boolean;
    updatedAt: any;
    createdAt: any;
    recentProjects: Array<{
      __typename?: "Project";
      id: string;
      name: string;
      createdBy: string;
    }>;
  };
};

export type SyncUserMutationVariables = Exact<{ [key: string]: never }>;

export type SyncUserMutation = {
  __typename?: "Mutation";
  syncUser: {
    __typename?: "UserProfile";
    _id: string;
    recentProjects: Array<{ __typename?: "Project"; id: string; name: string }>;
  };
};

export const CreateCharacterDocument = gql`
  mutation CreateCharacter($input: CreateCharacterInput!) {
    createCharacter(input: $input) {
      _id
      name
      image {
        url
      }
    }
  }
`;
export type CreateCharacterMutationFn = Apollo.MutationFunction<
  CreateCharacterMutation,
  CreateCharacterMutationVariables
>;

/**
 * __useCreateCharacterMutation__
 *
 * To run a mutation, you first call `useCreateCharacterMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCharacterMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCharacterMutation, { data, loading, error }] = useCreateCharacterMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateCharacterMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateCharacterMutation,
    CreateCharacterMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateCharacterMutation,
    CreateCharacterMutationVariables
  >(CreateCharacterDocument, options);
}
export type CreateCharacterMutationHookResult = ReturnType<
  typeof useCreateCharacterMutation
>;
export type CreateCharacterMutationResult =
  Apollo.MutationResult<CreateCharacterMutation>;
export type CreateCharacterMutationOptions = Apollo.BaseMutationOptions<
  CreateCharacterMutation,
  CreateCharacterMutationVariables
>;
export const GenerateCharacterDocument = gql`
  mutation GenerateCharacter(
    $params: JSON!
    $tool: String!
    $context: GenerationContextInput
  ) {
    generate(params: $params, tool: $tool, context: $context) {
      id
      progress
      status
    }
  }
`;
export type GenerateCharacterMutationFn = Apollo.MutationFunction<
  GenerateCharacterMutation,
  GenerateCharacterMutationVariables
>;

/**
 * __useGenerateCharacterMutation__
 *
 * To run a mutation, you first call `useGenerateCharacterMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateCharacterMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateCharacterMutation, { data, loading, error }] = useGenerateCharacterMutation({
 *   variables: {
 *      params: // value for 'params'
 *      tool: // value for 'tool'
 *      context: // value for 'context'
 *   },
 * });
 */
export function useGenerateCharacterMutation(
  baseOptions?: Apollo.MutationHookOptions<
    GenerateCharacterMutation,
    GenerateCharacterMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    GenerateCharacterMutation,
    GenerateCharacterMutationVariables
  >(GenerateCharacterDocument, options);
}
export type GenerateCharacterMutationHookResult = ReturnType<
  typeof useGenerateCharacterMutation
>;
export type GenerateCharacterMutationResult =
  Apollo.MutationResult<GenerateCharacterMutation>;
export type GenerateCharacterMutationOptions = Apollo.BaseMutationOptions<
  GenerateCharacterMutation,
  GenerateCharacterMutationVariables
>;
export const GenerateDocument = gql`
  mutation Generate(
    $params: JSON!
    $tool: String!
    $context: GenerationContextInput
  ) {
    generate(params: $params, tool: $tool, context: $context) {
      id
      progress
      status
    }
  }
`;
export type GenerateMutationFn = Apollo.MutationFunction<
  GenerateMutation,
  GenerateMutationVariables
>;

/**
 * __useGenerateMutation__
 *
 * To run a mutation, you first call `useGenerateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateMutation, { data, loading, error }] = useGenerateMutation({
 *   variables: {
 *      params: // value for 'params'
 *      tool: // value for 'tool'
 *      context: // value for 'context'
 *   },
 * });
 */
export function useGenerateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    GenerateMutation,
    GenerateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<GenerateMutation, GenerateMutationVariables>(
    GenerateDocument,
    options,
  );
}
export type GenerateMutationHookResult = ReturnType<typeof useGenerateMutation>;
export type GenerateMutationResult = Apollo.MutationResult<GenerateMutation>;
export type GenerateMutationOptions = Apollo.BaseMutationOptions<
  GenerateMutation,
  GenerateMutationVariables
>;
export const GenerationByProjectDocument = gql`
  subscription GenerationByProject($projectId: String!) {
    generationsByProject(projectId: $projectId) {
      id
      progress
      result {
        assets {
          url
          type
        }
      }
    }
  }
`;

/**
 * __useGenerationByProjectSubscription__
 *
 * To run a query within a React component, call `useGenerationByProjectSubscription` and pass it any options that fit your needs.
 * When your component renders, `useGenerationByProjectSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGenerationByProjectSubscription({
 *   variables: {
 *      projectId: // value for 'projectId'
 *   },
 * });
 */
export function useGenerationByProjectSubscription(
  baseOptions: Apollo.SubscriptionHookOptions<
    GenerationByProjectSubscription,
    GenerationByProjectSubscriptionVariables
  > &
    (
      | { variables: GenerationByProjectSubscriptionVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    GenerationByProjectSubscription,
    GenerationByProjectSubscriptionVariables
  >(GenerationByProjectDocument, options);
}
export type GenerationByProjectSubscriptionHookResult = ReturnType<
  typeof useGenerationByProjectSubscription
>;
export type GenerationByProjectSubscriptionResult =
  Apollo.SubscriptionResult<GenerationByProjectSubscription>;
export const GetGenerationsByProjectDocument = gql`
  subscription GetGenerationsByProject($projectId: String!) {
    generationsByProject(projectId: $projectId) {
      id
      params
      model
      progress
      provider
      result {
        assets {
          type
          url
        }
      }
      context {
        projectId
        sessionId
      }
    }
  }
`;

/**
 * __useGetGenerationsByProjectSubscription__
 *
 * To run a query within a React component, call `useGetGenerationsByProjectSubscription` and pass it any options that fit your needs.
 * When your component renders, `useGetGenerationsByProjectSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetGenerationsByProjectSubscription({
 *   variables: {
 *      projectId: // value for 'projectId'
 *   },
 * });
 */
export function useGetGenerationsByProjectSubscription(
  baseOptions: Apollo.SubscriptionHookOptions<
    GetGenerationsByProjectSubscription,
    GetGenerationsByProjectSubscriptionVariables
  > &
    (
      | {
          variables: GetGenerationsByProjectSubscriptionVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    GetGenerationsByProjectSubscription,
    GetGenerationsByProjectSubscriptionVariables
  >(GetGenerationsByProjectDocument, options);
}
export type GetGenerationsByProjectSubscriptionHookResult = ReturnType<
  typeof useGetGenerationsByProjectSubscription
>;
export type GetGenerationsByProjectSubscriptionResult =
  Apollo.SubscriptionResult<GetGenerationsByProjectSubscription>;
export const AvailableToolsDocument = gql`
  query AvailableTools {
    availableTools {
      category
      description
      key
      name
      pricing {
        baseCredits
        hasDynamicMultiplier
      }
      provider
      schema
    }
  }
`;

/**
 * __useAvailableToolsQuery__
 *
 * To run a query within a React component, call `useAvailableToolsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAvailableToolsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAvailableToolsQuery({
 *   variables: {
 *   },
 * });
 */
export function useAvailableToolsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    AvailableToolsQuery,
    AvailableToolsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AvailableToolsQuery, AvailableToolsQueryVariables>(
    AvailableToolsDocument,
    options,
  );
}
export function useAvailableToolsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AvailableToolsQuery,
    AvailableToolsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AvailableToolsQuery, AvailableToolsQueryVariables>(
    AvailableToolsDocument,
    options,
  );
}
export function useAvailableToolsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        AvailableToolsQuery,
        AvailableToolsQueryVariables
      >,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    AvailableToolsQuery,
    AvailableToolsQueryVariables
  >(AvailableToolsDocument, options);
}
export type AvailableToolsQueryHookResult = ReturnType<
  typeof useAvailableToolsQuery
>;
export type AvailableToolsLazyQueryHookResult = ReturnType<
  typeof useAvailableToolsLazyQuery
>;
export type AvailableToolsSuspenseQueryHookResult = ReturnType<
  typeof useAvailableToolsSuspenseQuery
>;
export type AvailableToolsQueryResult = Apollo.QueryResult<
  AvailableToolsQuery,
  AvailableToolsQueryVariables
>;
export const CharacterDocument = gql`
  query Character($characterId: ID!) {
    character(id: $characterId) {
      _id
      metadata
      image {
        url
      }
    }
  }
`;

/**
 * __useCharacterQuery__
 *
 * To run a query within a React component, call `useCharacterQuery` and pass it any options that fit your needs.
 * When your component renders, `useCharacterQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCharacterQuery({
 *   variables: {
 *      characterId: // value for 'characterId'
 *   },
 * });
 */
export function useCharacterQuery(
  baseOptions: Apollo.QueryHookOptions<
    CharacterQuery,
    CharacterQueryVariables
  > &
    (
      | { variables: CharacterQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CharacterQuery, CharacterQueryVariables>(
    CharacterDocument,
    options,
  );
}
export function useCharacterLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CharacterQuery,
    CharacterQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CharacterQuery, CharacterQueryVariables>(
    CharacterDocument,
    options,
  );
}
export function useCharacterSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<CharacterQuery, CharacterQueryVariables>,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<CharacterQuery, CharacterQueryVariables>(
    CharacterDocument,
    options,
  );
}
export type CharacterQueryHookResult = ReturnType<typeof useCharacterQuery>;
export type CharacterLazyQueryHookResult = ReturnType<
  typeof useCharacterLazyQuery
>;
export type CharacterSuspenseQueryHookResult = ReturnType<
  typeof useCharacterSuspenseQuery
>;
export type CharacterQueryResult = Apollo.QueryResult<
  CharacterQuery,
  CharacterQueryVariables
>;
export const CharactersDocument = gql`
  query Characters {
    characters {
      _id
      name
      image {
        url
      }
    }
  }
`;

/**
 * __useCharactersQuery__
 *
 * To run a query within a React component, call `useCharactersQuery` and pass it any options that fit your needs.
 * When your component renders, `useCharactersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCharactersQuery({
 *   variables: {
 *   },
 * });
 */
export function useCharactersQuery(
  baseOptions?: Apollo.QueryHookOptions<
    CharactersQuery,
    CharactersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CharactersQuery, CharactersQueryVariables>(
    CharactersDocument,
    options,
  );
}
export function useCharactersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CharactersQuery,
    CharactersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CharactersQuery, CharactersQueryVariables>(
    CharactersDocument,
    options,
  );
}
export function useCharactersSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        CharactersQuery,
        CharactersQueryVariables
      >,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<CharactersQuery, CharactersQueryVariables>(
    CharactersDocument,
    options,
  );
}
export type CharactersQueryHookResult = ReturnType<typeof useCharactersQuery>;
export type CharactersLazyQueryHookResult = ReturnType<
  typeof useCharactersLazyQuery
>;
export type CharactersSuspenseQueryHookResult = ReturnType<
  typeof useCharactersSuspenseQuery
>;
export type CharactersQueryResult = Apollo.QueryResult<
  CharactersQuery,
  CharactersQueryVariables
>;
export const GetGenerationDocument = gql`
  query GetGeneration($generationId: String!) {
    generation(id: $generationId) {
      context {
        characterId
        characterVariantId
        organizationId
        parentAssetId
        projectId
        sessionId
        storyboardId
        userId
        workspaceId
      }
      createdAt
      error
      id
      model
      params
      progress
      provider
      result {
        assets {
          url
          type
        }
        metadata
      }
      status
      tool
      updatedAt
    }
  }
`;

/**
 * __useGetGenerationQuery__
 *
 * To run a query within a React component, call `useGetGenerationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetGenerationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetGenerationQuery({
 *   variables: {
 *      generationId: // value for 'generationId'
 *   },
 * });
 */
export function useGetGenerationQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetGenerationQuery,
    GetGenerationQueryVariables
  > &
    (
      | { variables: GetGenerationQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetGenerationQuery, GetGenerationQueryVariables>(
    GetGenerationDocument,
    options,
  );
}
export function useGetGenerationLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetGenerationQuery,
    GetGenerationQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetGenerationQuery, GetGenerationQueryVariables>(
    GetGenerationDocument,
    options,
  );
}
export function useGetGenerationSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        GetGenerationQuery,
        GetGenerationQueryVariables
      >,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetGenerationQuery,
    GetGenerationQueryVariables
  >(GetGenerationDocument, options);
}
export type GetGenerationQueryHookResult = ReturnType<
  typeof useGetGenerationQuery
>;
export type GetGenerationLazyQueryHookResult = ReturnType<
  typeof useGetGenerationLazyQuery
>;
export type GetGenerationSuspenseQueryHookResult = ReturnType<
  typeof useGetGenerationSuspenseQuery
>;
export type GetGenerationQueryResult = Apollo.QueryResult<
  GetGenerationQuery,
  GetGenerationQueryVariables
>;
export const HealthDocument = gql`
  query Health {
    health {
      id
      service
      status
      timestamp
      version
    }
  }
`;

/**
 * __useHealthQuery__
 *
 * To run a query within a React component, call `useHealthQuery` and pass it any options that fit your needs.
 * When your component renders, `useHealthQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useHealthQuery({
 *   variables: {
 *   },
 * });
 */
export function useHealthQuery(
  baseOptions?: Apollo.QueryHookOptions<HealthQuery, HealthQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<HealthQuery, HealthQueryVariables>(
    HealthDocument,
    options,
  );
}
export function useHealthLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<HealthQuery, HealthQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<HealthQuery, HealthQueryVariables>(
    HealthDocument,
    options,
  );
}
export function useHealthSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<HealthQuery, HealthQueryVariables>,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<HealthQuery, HealthQueryVariables>(
    HealthDocument,
    options,
  );
}
export type HealthQueryHookResult = ReturnType<typeof useHealthQuery>;
export type HealthLazyQueryHookResult = ReturnType<typeof useHealthLazyQuery>;
export type HealthSuspenseQueryHookResult = ReturnType<
  typeof useHealthSuspenseQuery
>;
export type HealthQueryResult = Apollo.QueryResult<
  HealthQuery,
  HealthQueryVariables
>;
export const ProfileDocument = gql`
  query Profile {
    profile {
      _id
      email
      firstName
      lastName
      role
      recentProjects {
        id
        name
        createdBy
      }
      isActive
      updatedAt
      createdAt
    }
  }
`;

/**
 * __useProfileQuery__
 *
 * To run a query within a React component, call `useProfileQuery` and pass it any options that fit your needs.
 * When your component renders, `useProfileQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useProfileQuery({
 *   variables: {
 *   },
 * });
 */
export function useProfileQuery(
  baseOptions?: Apollo.QueryHookOptions<ProfileQuery, ProfileQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ProfileQuery, ProfileQueryVariables>(
    ProfileDocument,
    options,
  );
}
export function useProfileLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    ProfileQuery,
    ProfileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ProfileQuery, ProfileQueryVariables>(
    ProfileDocument,
    options,
  );
}
export function useProfileSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ProfileQuery, ProfileQueryVariables>,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ProfileQuery, ProfileQueryVariables>(
    ProfileDocument,
    options,
  );
}
export type ProfileQueryHookResult = ReturnType<typeof useProfileQuery>;
export type ProfileLazyQueryHookResult = ReturnType<typeof useProfileLazyQuery>;
export type ProfileSuspenseQueryHookResult = ReturnType<
  typeof useProfileSuspenseQuery
>;
export type ProfileQueryResult = Apollo.QueryResult<
  ProfileQuery,
  ProfileQueryVariables
>;
export const SyncUserDocument = gql`
  mutation SyncUser {
    syncUser {
      _id
      recentProjects {
        id
        name
      }
    }
  }
`;
export type SyncUserMutationFn = Apollo.MutationFunction<
  SyncUserMutation,
  SyncUserMutationVariables
>;

/**
 * __useSyncUserMutation__
 *
 * To run a mutation, you first call `useSyncUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSyncUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [syncUserMutation, { data, loading, error }] = useSyncUserMutation({
 *   variables: {
 *   },
 * });
 */
export function useSyncUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SyncUserMutation,
    SyncUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<SyncUserMutation, SyncUserMutationVariables>(
    SyncUserDocument,
    options,
  );
}
export type SyncUserMutationHookResult = ReturnType<typeof useSyncUserMutation>;
export type SyncUserMutationResult = Apollo.MutationResult<SyncUserMutation>;
export type SyncUserMutationOptions = Apollo.BaseMutationOptions<
  SyncUserMutation,
  SyncUserMutationVariables
>;
