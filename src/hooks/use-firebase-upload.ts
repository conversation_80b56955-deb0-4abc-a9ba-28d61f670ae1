import { useState, useCallback } from "react";
import {
  uploadFile,
  deleteFile,
  UploadOptions,
  UploadResult,
} from "@/lib/firebase-storage";

interface UseFirebaseUploadOptions extends UploadOptions {
  onSuccess?: (result: UploadResult) => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
}

export interface UseFirebaseUploadReturn {
  upload: (file: File) => Promise<UploadResult | null>;
  deleteFile: (path: string) => Promise<boolean>;
  uploading: boolean;
  progress: number;
  error: Error | null;
  result: UploadResult | null;
  reset: () => void;
}

export const useFirebaseUpload = (
  options: UseFirebaseUploadOptions = {}
): UseFirebaseUploadReturn => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);
  const [result, setResult] = useState<UploadResult | null>(null);

  const upload = useCallback(
    async (file: File): Promise<UploadResult | null> => {
      setUploading(true);
      setProgress(0);
      setError(null);
      setResult(null);

      try {
        const uploadResult = await uploadFile(
          file,
          options,
          progressPercent => {
            setProgress(progressPercent);
            options.onProgress?.(progressPercent);
          }
        );

        setResult(uploadResult);
        options.onSuccess?.(uploadResult);
        return uploadResult;
      } catch (err) {
        const error = err instanceof Error ? err : new Error("Upload failed");
        setError(error);
        options.onError?.(error);
        return null;
      } finally {
        setUploading(false);
      }
    },
    [options]
  );

  const deleteFileHandler = useCallback(
    async (path: string): Promise<boolean> => {
      try {
        await deleteFile(path);
        return true;
      } catch (err) {
        const error = err instanceof Error ? err : new Error("Delete failed");
        setError(error);
        options.onError?.(error);
        return false;
      }
    },
    [options]
  );

  const reset = useCallback(() => {
    setUploading(false);
    setProgress(0);
    setError(null);
    setResult(null);
  }, []);

  return {
    upload,
    deleteFile: deleteFileHandler,
    uploading,
    progress,
    error,
    result,
    reset,
  };
};
