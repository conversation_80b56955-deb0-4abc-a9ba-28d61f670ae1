"use client";
import { useUser } from "@/contexts/user-context";
import { v4 as uuidv4 } from "uuid";
import { GenerationContextInput } from "@/graphql/generated/graphql";

export type UseGenerationContextParamsType = Omit<
  GenerationContextInput,
  "projectId" | "sessionId"
>;
export type UseGenerationContextType = (
  params: UseGenerationContextParamsType
) => {
  generateContext: () => GenerationContextInput & {
    projectId: string;
    sessionId: string;
  };
};

const useGenerationContext: UseGenerationContextType = params => {
  const { defaultProjectId } = useUser();

  const generateContext = () => {
    if (!defaultProjectId) {
      throw new Error("Default project ID is not set in user context.");
    }
    return {
      ...params,
      projectId: defaultProjectId,
      sessionId: uuidv4(),
    };
  };

  return { generateContext } as const;
};

export default useGenerationContext;
