"use client";

import { useAuth } from "@clerk/nextjs";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";

interface UseAuthRedirectOptions {
  redirectTo?: string;
  redirectIfAuthenticated?: boolean;
  redirectIfUnauthenticated?: boolean;
}

export function useAuthRedirect(options: UseAuthRedirectOptions = {}) {
  const { isSignedIn, isLoaded } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const {
    redirectTo = "/dashboard",
    redirectIfAuthenticated = false,
    redirectIfUnauthenticated = false,
  } = options;

  useEffect(() => {
    if (!isLoaded) return;

    if (redirectIfAuthenticated && isSignedIn) {
      router.push(redirectTo);
    }

    if (redirectIfUnauthenticated && !isSignedIn) {
      router.push(`/sign-in?redirect_url=${encodeURIComponent(pathname)}`);
    }
  }, [
    isLoaded,
    isSignedIn,
    redirectIfAuthenticated,
    redirectIfUnauthenticated,
    redirectTo,
    router,
    pathname,
  ]);

  return {
    isSignedIn,
    isLoaded,
    redirect: (url: string) => router.push(url),
  };
}
