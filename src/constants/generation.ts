/**
 * Centralized constants for generation tools
 * These should match the backend values defined in gateway/libs/tools/definitions/comfy/common.ts
 */

// Visual style enums used across multiple generation tools
export enum GenerationStyle {
  THREE_D_ANIMATION = "THREE_D_ANIMATION",
  GHIBLI = "GHIBLI",
  COMIC_BOOK = "COMIC_BOOK",
}

// Image orientation options
export enum ImageOrientation {
  LANDSCAPE = "LANDSCAPE",
  PORTRAIT = "PORTRAIT",
}

// Character size options
export enum CharacterSize {
  XS = "xs",
  S = "s",
  M = "m",
  L = "l",
  XL = "xl",
}

// Aspect ratio options for Flux
export enum FluxAspectRatio {
  SQUARE = "square",
  PORTRAIT = "portrait",
  LANDSCAPE = "landscape",
  LANDSCAPE_16_9 = "landscape_16_9",
}

// Pre-configured style options for select fields
export const STYLE_OPTIONS = [
  { value: GenerationStyle.THREE_D_ANIMATION, label: "3D Animation" },
  { value: GenerationStyle.GHIBLI, label: "Studio Ghibli" },
  { value: GenerationStyle.COMIC_BOOK, label: "Comic Book" },
];

// Pre-configured orientation options for select fields
export const ORIENTATION_OPTIONS = [
  { value: ImageOrientation.LANDSCAPE, label: "Landscape" },
  { value: ImageOrientation.PORTRAIT, label: "Portrait" },
];

// Pre-configured character size options for select fields
export const CHARACTER_SIZE_OPTIONS = [
  { value: CharacterSize.XS, label: "XS" },
  { value: CharacterSize.S, label: "S" },
  { value: CharacterSize.M, label: "M" },
  { value: CharacterSize.L, label: "L" },
  { value: CharacterSize.XL, label: "XL" },
];

// Pre-configured Flux aspect ratio options
export const FLUX_ASPECT_RATIO_OPTIONS = [
  { value: FluxAspectRatio.SQUARE, label: "Square (1:1)" },
  //{ value: FluxAspectRatio.PORTRAIT, label: "Portrait (3:4)" },
  //{ value: FluxAspectRatio.LANDSCAPE, label: "Landscape (4:3)" },
  { value: FluxAspectRatio.LANDSCAPE_16_9, label: "Widescreen (16:9)" },
];

// Default values
export const DEFAULT_STYLE = GenerationStyle.THREE_D_ANIMATION;
export const DEFAULT_ORIENTATION = ImageOrientation.LANDSCAPE;
export const DEFAULT_CHARACTER_SIZE = CharacterSize.M;
export const DEFAULT_SEED = -1;
export const DEFAULT_NUMBER_OF_IMAGES = 1;
