import React, { ReactNode } from "react";
import { ApolloWrapper } from "@/lib/apollo/apollo-provider";
import { UserProvider } from "@/contexts/user-context";
import { FirebaseProvider } from "@/contexts/firebase-context";

interface ProvidersProps {
  children: ReactNode;
}

const AuthenticatedProviders = ({ children }: ProvidersProps) => {
  return (
    <ApolloWrapper>
      <FirebaseProvider>
        <UserProvider>{children}</UserProvider>
      </FirebaseProvider>
    </ApolloWrapper>
  );
};

export default AuthenticatedProviders;
