"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Hero<PERSON>Provider } from "@heroui/react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ReactNode } from "react";

interface ProvidersProps {
  children: ReactNode;
}

export function GlobalProviders({ children }: ProvidersProps) {
  return (
    <ClerkProvider>
      <NextThemesProvider
        attribute="class"
        defaultTheme="light"
        themes={["light", "dark", "ministudio"]}
      >
        <HeroUIProvider>{children}</HeroUIProvider>
      </NextThemesProvider>
    </ClerkProvider>
  );
}
